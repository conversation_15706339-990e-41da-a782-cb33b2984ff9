__pycache__/pylab.cpython-313.pyc,,
matplotlib-3.10.6.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
matplotlib-3.10.6.dist-info/LICENSE,sha256=WhqB6jAXKMi7opM9qDLAzWIina8giToCSrPVMkRGjbw,4830
matplotlib-3.10.6.dist-info/METADATA,sha256=mPi22uTz5Eb6RMhoTcU9tPMAPv3h7xbPjEEb7g_vK7E,11268
matplotlib-3.10.6.dist-info/RECORD,,
matplotlib-3.10.6.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib-3.10.6.dist-info/WHEEL,sha256=suq8ARrxbiI7iLH3BgK-82uzxQ-4Hm-m8w01oCokrtA,85
matplotlib/__init__.py,sha256=xsR9Hs8l15NPOMilXDuOEW1BqgjY7PqC9q6K9Ykhajo,55823
matplotlib/__init__.pyi,sha256=s0YJ8fkGVioZL9bcBtyYgCq_MbY8LRrQUtmaD3XaIxQ,3439
matplotlib/__pycache__/__init__.cpython-313.pyc,,
matplotlib/__pycache__/_afm.cpython-313.pyc,,
matplotlib/__pycache__/_animation_data.cpython-313.pyc,,
matplotlib/__pycache__/_blocking_input.cpython-313.pyc,,
matplotlib/__pycache__/_cm.cpython-313.pyc,,
matplotlib/__pycache__/_cm_bivar.cpython-313.pyc,,
matplotlib/__pycache__/_cm_listed.cpython-313.pyc,,
matplotlib/__pycache__/_cm_multivar.cpython-313.pyc,,
matplotlib/__pycache__/_color_data.cpython-313.pyc,,
matplotlib/__pycache__/_constrained_layout.cpython-313.pyc,,
matplotlib/__pycache__/_docstring.cpython-313.pyc,,
matplotlib/__pycache__/_enums.cpython-313.pyc,,
matplotlib/__pycache__/_fontconfig_pattern.cpython-313.pyc,,
matplotlib/__pycache__/_internal_utils.cpython-313.pyc,,
matplotlib/__pycache__/_layoutgrid.cpython-313.pyc,,
matplotlib/__pycache__/_mathtext.cpython-313.pyc,,
matplotlib/__pycache__/_mathtext_data.cpython-313.pyc,,
matplotlib/__pycache__/_pylab_helpers.cpython-313.pyc,,
matplotlib/__pycache__/_text_helpers.cpython-313.pyc,,
matplotlib/__pycache__/_tight_bbox.cpython-313.pyc,,
matplotlib/__pycache__/_tight_layout.cpython-313.pyc,,
matplotlib/__pycache__/_type1font.cpython-313.pyc,,
matplotlib/__pycache__/_version.cpython-313.pyc,,
matplotlib/__pycache__/animation.cpython-313.pyc,,
matplotlib/__pycache__/artist.cpython-313.pyc,,
matplotlib/__pycache__/axis.cpython-313.pyc,,
matplotlib/__pycache__/backend_bases.cpython-313.pyc,,
matplotlib/__pycache__/backend_managers.cpython-313.pyc,,
matplotlib/__pycache__/backend_tools.cpython-313.pyc,,
matplotlib/__pycache__/bezier.cpython-313.pyc,,
matplotlib/__pycache__/category.cpython-313.pyc,,
matplotlib/__pycache__/cbook.cpython-313.pyc,,
matplotlib/__pycache__/cm.cpython-313.pyc,,
matplotlib/__pycache__/collections.cpython-313.pyc,,
matplotlib/__pycache__/colorbar.cpython-313.pyc,,
matplotlib/__pycache__/colorizer.cpython-313.pyc,,
matplotlib/__pycache__/colors.cpython-313.pyc,,
matplotlib/__pycache__/container.cpython-313.pyc,,
matplotlib/__pycache__/contour.cpython-313.pyc,,
matplotlib/__pycache__/dates.cpython-313.pyc,,
matplotlib/__pycache__/dviread.cpython-313.pyc,,
matplotlib/__pycache__/figure.cpython-313.pyc,,
matplotlib/__pycache__/font_manager.cpython-313.pyc,,
matplotlib/__pycache__/gridspec.cpython-313.pyc,,
matplotlib/__pycache__/hatch.cpython-313.pyc,,
matplotlib/__pycache__/image.cpython-313.pyc,,
matplotlib/__pycache__/inset.cpython-313.pyc,,
matplotlib/__pycache__/layout_engine.cpython-313.pyc,,
matplotlib/__pycache__/legend.cpython-313.pyc,,
matplotlib/__pycache__/legend_handler.cpython-313.pyc,,
matplotlib/__pycache__/lines.cpython-313.pyc,,
matplotlib/__pycache__/markers.cpython-313.pyc,,
matplotlib/__pycache__/mathtext.cpython-313.pyc,,
matplotlib/__pycache__/mlab.cpython-313.pyc,,
matplotlib/__pycache__/offsetbox.cpython-313.pyc,,
matplotlib/__pycache__/patches.cpython-313.pyc,,
matplotlib/__pycache__/path.cpython-313.pyc,,
matplotlib/__pycache__/patheffects.cpython-313.pyc,,
matplotlib/__pycache__/pylab.cpython-313.pyc,,
matplotlib/__pycache__/pyplot.cpython-313.pyc,,
matplotlib/__pycache__/quiver.cpython-313.pyc,,
matplotlib/__pycache__/rcsetup.cpython-313.pyc,,
matplotlib/__pycache__/sankey.cpython-313.pyc,,
matplotlib/__pycache__/scale.cpython-313.pyc,,
matplotlib/__pycache__/spines.cpython-313.pyc,,
matplotlib/__pycache__/stackplot.cpython-313.pyc,,
matplotlib/__pycache__/streamplot.cpython-313.pyc,,
matplotlib/__pycache__/table.cpython-313.pyc,,
matplotlib/__pycache__/texmanager.cpython-313.pyc,,
matplotlib/__pycache__/text.cpython-313.pyc,,
matplotlib/__pycache__/textpath.cpython-313.pyc,,
matplotlib/__pycache__/ticker.cpython-313.pyc,,
matplotlib/__pycache__/transforms.cpython-313.pyc,,
matplotlib/__pycache__/typing.cpython-313.pyc,,
matplotlib/__pycache__/units.cpython-313.pyc,,
matplotlib/__pycache__/widgets.cpython-313.pyc,,
matplotlib/_afm.py,sha256=cWe1Ib37T6ZyHbR6_hPuzAjotMmi32y-kDB-i28iyqE,16692
matplotlib/_animation_data.py,sha256=JJJbbc-fMdPjkbQ7ng9BHL5i91VTDHQVTtEdWOvWBAI,7986
matplotlib/_api/__init__.py,sha256=7Fs57FtnCmbyr_NwuGWe0EhfXOYrBw22iKMU9onGc_k,13799
matplotlib/_api/__init__.pyi,sha256=XNL-oGkk1MZPtSXk3rHlC3jLWZxsElmpNpOongKq8qA,2246
matplotlib/_api/__pycache__/__init__.cpython-313.pyc,,
matplotlib/_api/__pycache__/deprecation.cpython-313.pyc,,
matplotlib/_api/deprecation.py,sha256=XJxksSV8ukS8kSVmsGPS0zgv1ffFa8WNaRLAk31dmOM,20091
matplotlib/_api/deprecation.pyi,sha256=A8De57amX2GlZSrDYwVYCMxHFb8AsYGrH0k_bCxuJus,2217
matplotlib/_blocking_input.py,sha256=VHNsxvX2mTx_xBknd30MSicVlRXS4dCDe9hDctbV5rk,1224
matplotlib/_c_internal_utils.cp313-win_amd64.pyd,sha256=-HpBC-HF5mzgQ_FSUyqcl51atJC8ntgHULgQScBY_WA,152064
matplotlib/_c_internal_utils.pyi,sha256=Z3bLs9pMGXrmZjt-4_A-x4321bLP-B54xDbr4PIgUfc,377
matplotlib/_cm.py,sha256=PuYIAkUpz4u4aiUjvdV5njIfG0J_MQY9pM2Yz3j3KXs,68014
matplotlib/_cm_bivar.py,sha256=gpmKiSxsWoVGWIifseh0goND7Y7zTWJKsznr_QkEtDg,97461
matplotlib/_cm_listed.py,sha256=3a02mPUSnOsXkqRFNxKGwIvJCAKY5lezBUrqGcllnvk,135004
matplotlib/_cm_multivar.py,sha256=0UjNFW7Sytj1t31QD99ebfr-al7NmCpWhKUOyq9VzK8,6630
matplotlib/_color_data.py,sha256=k-wdTi6ArJxksqBfMT-7Uy2qWz8XX4Th5gsjf32CwmM,34780
matplotlib/_color_data.pyi,sha256=RdBRk01yuf3jYVlCwG351tIBCxehizkZMnKs9c8gnOw,170
matplotlib/_constrained_layout.py,sha256=XX_2elqHukF3toeEDupwQPlN_8UEv3-yGKlR0A92n74,31485
matplotlib/_docstring.py,sha256=u9yJorJidI8k1W8S01SIMqNmh5VOMVc-AqyES66EEdk,4435
matplotlib/_docstring.pyi,sha256=6ze5DoqZaFy6BQ5Z8vtDiJwSOXhBcpZJlkM5enI9cuE,800
matplotlib/_enums.py,sha256=euD2sj2FIbQMzIPA4rCrII_y8RVzaMyEBKsSdBQaUb4,6175
matplotlib/_enums.pyi,sha256=K7j_kDwGOnx37CYnXhwfJ9NJBGeet45KvAj0om05RUs,326
matplotlib/_fontconfig_pattern.py,sha256=2livocARMbpys8tmicH6wlifcwcNoIstgruSY6DSAfk,4361
matplotlib/_image.cp313-win_amd64.pyd,sha256=Jr-Wg2nVjJZXyHPOT07QzLBT4eRDmXYp3wk0-BI9V_s,355840
matplotlib/_image.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/_internal_utils.py,sha256=nhK6LLWYW93fBcsFiO09JmqFj2rgHEsGYFOeaC7HRKw,2140
matplotlib/_layoutgrid.py,sha256=mFB9asZVol2aV9hLBpdntuG5x1isKUrajF-3TUhCgso,21676
matplotlib/_mathtext.py,sha256=kKFME5pnU2TZR_sD2Dm2KsnR0ldkmIgY3N6lgqYiSII,107848
matplotlib/_mathtext_data.py,sha256=9y__7jf3bzgOmD2lEYW3QpgsT-z9QDuiwRLVH7Wq8Pw,65067
matplotlib/_path.cp313-win_amd64.pyd,sha256=qmHP1XXifKzo2tTiH5x5yFgrUdqovQfVO-bKx2cdbwE,311296
matplotlib/_path.pyi,sha256=yznyfzoUogH9vvi0vK68ga4Shlbrn5UBhAnLX8Ght1o,325
matplotlib/_pylab_helpers.py,sha256=pJERytHDmXo2VP3sH9Qw6NwJP0hefNPQMUXCVntnoHI,4307
matplotlib/_pylab_helpers.pyi,sha256=7OZKr-OL3ipVt1EDZ6e-tRwHASz-ijYfcIdlPczXhvQ,1012
matplotlib/_qhull.cp313-win_amd64.pyd,sha256=crmDRxG3PKOmxXzz4VN-WR6Zz2VwOlXqVMN6KvqjG3k,541696
matplotlib/_qhull.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/_text_helpers.py,sha256=nz1pvEp8756PCrn6KIY2M6vVA-beYUNODvvf4NnGF0g,2538
matplotlib/_tight_bbox.py,sha256=ddJ5ViXulbPLocHr-RkWK27WJSuV2WXUx74jZyL0NOg,2787
matplotlib/_tight_layout.py,sha256=A3vZKBmxci7w35OO9lxgfUqrRKgTr-N_dUza0M05WxE,12675
matplotlib/_tri.cp313-win_amd64.pyd,sha256=oBTkWjBjn4qx220dUiJkJH7p_-OeWm_pZdTt2qso6mY,294912
matplotlib/_tri.pyi,sha256=bFnu97pIlSboJT3ijQBw4YlZdlR1UzXYtI3DkEPVzeM,1429
matplotlib/_type1font.py,sha256=c_PXvyOFQ4YCRQJ23snWNkdEhuZ4k0JbLe55zaeoXfQ,28409
matplotlib/_version.py,sha256=VC9SRUcNAqaFBUs-eTHhFGNx5sumClw4yPTuFOTUDeE,19
matplotlib/animation.py,sha256=58vUSy_0M2Ve2GO5JcXsCHxCwr7TqaaMed-KNxN6Nno,73114
matplotlib/animation.pyi,sha256=C8Fgmswh4um-qYgqPZNBEpXVaefRXi6e026pYjonLHg,6566
matplotlib/artist.py,sha256=hfCGETRkHLfP-i4PmmNsc7syXyFTZi4HYvzyE8q9ak0,63293
matplotlib/artist.pyi,sha256=9sR0w8Kvi1GaFV6IbUzOw9LvQkPEB8FP_uRvUcI6_hg,7336
matplotlib/axes/__init__.py,sha256=aHE_zIjphIJkW4_1fyoFuEWTb7gCRewK5jNZWJHEdgM,351
matplotlib/axes/__init__.pyi,sha256=HP1z2v-PboHQS4dQjvJ7XjUjX-zw6taZRTTB9oVKwYE,303
matplotlib/axes/__pycache__/__init__.cpython-313.pyc,,
matplotlib/axes/__pycache__/_axes.cpython-313.pyc,,
matplotlib/axes/__pycache__/_base.cpython-313.pyc,,
matplotlib/axes/__pycache__/_secondary_axes.cpython-313.pyc,,
matplotlib/axes/_axes.py,sha256=CwBUFh4fIlJF4Km80apFeH9sPVBi-3OPTOnxqKveZ5Q,353841
matplotlib/axes/_axes.pyi,sha256=fvlVj8AnKt52EOCgxJ5N_6_lb9n0gmXlOP7WGf2puNI,26116
matplotlib/axes/_base.py,sha256=eXarXIkk1SiQRPa7Rdu_R6gDgHdm2l60Hh2RPtPTf7E,186445
matplotlib/axes/_base.pyi,sha256=EX6xYlHGEMRWXoE85w-UfCkN7m990Gg5VFZJ44D0XmE,17058
matplotlib/axes/_secondary_axes.py,sha256=Sew-LK57mDndUXb-9_VHzs5dJLQ6fh4nstZtfpZjui0,11887
matplotlib/axes/_secondary_axes.pyi,sha256=GtU55YzLNN7XHMRQcAmcAxcdcN-FzHw1RAJpOiTcZFk,1414
matplotlib/axis.py,sha256=bsPEyn4eMAnkTCbW4rxSm8ysPNYH2p_ur7RN_ZfZRps,104714
matplotlib/axis.pyi,sha256=UO1-oCeHyB7Mrxxnuo0Qys2u_k8e6qTmz4B6-rHrWdw,10181
matplotlib/backend_bases.py,sha256=5EaIYhLC7LdKrk0SYSNBiYW3G16KbbexndFhoOX7_jY,131953
matplotlib/backend_bases.pyi,sha256=G6MqvmWf-y2SwhuhNF2qN8hmovxAkV1TJMbg4LzU9cM,16270
matplotlib/backend_managers.py,sha256=RQheCO_cQBlaWsYMbAmswu0UPKU7bmLTI5LEFgotklA,11795
matplotlib/backend_managers.pyi,sha256=agnuM0wiZRqSgqti2AgbKJijRLvEPNLOrSY8PEwLjFE,2253
matplotlib/backend_tools.py,sha256=eCx84l8JBYayju5Ry1LUFyKLIrdgxmGUTJw72VNP1yo,33186
matplotlib/backend_tools.pyi,sha256=lc3W6FcY0XhTb8Z8XAqQ6MBk_SV7WH41t7zjwAlQfmE,4122
matplotlib/backends/__init__.py,sha256=JowJe-tDrUBMNJTiJATgiEuACpgdxsKnRCYa-nC255A,206
matplotlib/backends/__pycache__/__init__.cpython-313.pyc,,
matplotlib/backends/__pycache__/_backend_gtk.cpython-313.pyc,,
matplotlib/backends/__pycache__/_backend_pdf_ps.cpython-313.pyc,,
matplotlib/backends/__pycache__/_backend_tk.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_agg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_cairo.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_gtk3.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_gtk3agg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_gtk3cairo.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_gtk4.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_gtk4agg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_gtk4cairo.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_macosx.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_mixed.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_nbagg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_pdf.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_pgf.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_ps.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_qt.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_qt5.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_qt5agg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_qt5cairo.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_qtagg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_qtcairo.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_svg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_template.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_tkagg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_tkcairo.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_webagg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_webagg_core.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_wx.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_wxagg.cpython-313.pyc,,
matplotlib/backends/__pycache__/backend_wxcairo.cpython-313.pyc,,
matplotlib/backends/__pycache__/qt_compat.cpython-313.pyc,,
matplotlib/backends/__pycache__/registry.cpython-313.pyc,,
matplotlib/backends/_backend_agg.cp313-win_amd64.pyd,sha256=64GmehC4CnTVVUP8l-PzuQ1u4OWkUt4yLub_041SfPY,486400
matplotlib/backends/_backend_agg.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/_backend_gtk.py,sha256=zNCmMOgjgZGfAIlxDU4ph02XMSSn0wIUoPQ4wsaeeAg,11274
matplotlib/backends/_backend_pdf_ps.py,sha256=M0kjQpMlBoAim6Pdw8u3FqUIoXz4UwRv4mSzXPJPI44,5968
matplotlib/backends/_backend_tk.py,sha256=zx9GZYgrcmTwDgUT3rPvU7YgW79sUbJIWQ-SJtEsH5s,44581
matplotlib/backends/_macosx.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/_tkagg.cp313-win_amd64.pyd,sha256=gstPdJuMo65OrzfEFYbCrp6k12s6tySBUC3BD04fWtw,166912
matplotlib/backends/_tkagg.pyi,sha256=1yKnYKSgNFesrb0ZI5RQCyTO4zONgHW5MrJ9vq4HzyI,379
matplotlib/backends/backend_agg.py,sha256=Py_rXRbDTP0ZSrNB2O5Tmh5V8G5V9ViXH_YV5PTX9xw,19888
matplotlib/backends/backend_cairo.py,sha256=Ah5M85Ppa8zYGOtLOizW0gzltjs3b4nD56qFeQR795U,18618
matplotlib/backends/backend_gtk3.py,sha256=ZHf4CKhDGJvl8J3p6Sx_ZJBTgVetZBbaKWfnXKvNe0c,22277
matplotlib/backends/backend_gtk3agg.py,sha256=aFJw05L5V-FyFSj_20l_FuC5piPf8fhKzft_m1NMhYc,2463
matplotlib/backends/backend_gtk3cairo.py,sha256=zPsJzVm750if2LiQ9ybzsbX0rBhkP05XVvR9Lhz65so,1392
matplotlib/backends/backend_gtk4.py,sha256=lkcW3cEKdASQ4vD6VHBUXvZQZEGvj5uenCVg4fHof_g,23541
matplotlib/backends/backend_gtk4agg.py,sha256=00i3qpIt9Tcf_S74GOWbeckiPlfVJoQ2pBbhXDMthF0,1262
matplotlib/backends/backend_gtk4cairo.py,sha256=sqWm3WgfNO8EsBjqzsD4U4cAc0f4q5SufYx7ZJacDf0,1125
matplotlib/backends/backend_macosx.py,sha256=Gjbu1MKXIZpCM_luMG6U7qDgwotcGSegCvYw-NNWxeQ,7397
matplotlib/backends/backend_mixed.py,sha256=Gf_2BDjy94FZRXALm0X3xRpK_1l8bZGSYCqnrSjrjME,4696
matplotlib/backends/backend_nbagg.py,sha256=Au9RHfRufpI0ngT4R0K0CUVtAMFi9Bg-YhDunlj_Lko,8000
matplotlib/backends/backend_pdf.py,sha256=DfFKdD4-wc7PPpfjXh8MAaaDppXUvLwicaMhXKgRhGw,105320
matplotlib/backends/backend_pgf.py,sha256=GT7UHkAtHtnZcyV0GxHtUL9TTre9dji7Q6qexyi4AUU,39567
matplotlib/backends/backend_ps.py,sha256=JRZZ6hsPcCfZmb-HczqyBulgow15yvoaHQe3Zcl1Zd4,51991
matplotlib/backends/backend_qt.py,sha256=FjwUd9E2Fu2gCUhAVcJjXC--sAJsAoLUEHpMpa-IueE,42312
matplotlib/backends/backend_qt5.py,sha256=kzfoo2ksEGsiWAa2LGtZYzKvfzqJJWyGOohohcRAu1g,787
matplotlib/backends/backend_qt5agg.py,sha256=Vh7H8kqWH4X8a3VX2XZ2Vze9srwJavkNHAZxdJUz_bk,352
matplotlib/backends/backend_qt5cairo.py,sha256=Go2Y0GVkXh1xh6x4F255_e5Xbwwws-OiD1Fc0805E78,292
matplotlib/backends/backend_qtagg.py,sha256=ZjPtp5wR6tZGjbngPXRdVXYRhiPPrc5C0q2DmtdRkpY,3413
matplotlib/backends/backend_qtcairo.py,sha256=e3SUG50VGqo68eS_8ebTCVQPa4AaxLxuo1JiWX4TIWg,1770
matplotlib/backends/backend_svg.py,sha256=RQvPE9pR0xSu55NIMajX1WBqmmXbUWWwdWr2qMG-63k,51000
matplotlib/backends/backend_template.py,sha256=qhWvXGiPeO2jvH61L3NCRYLReo--d4WJrQYanIaEmiE,8012
matplotlib/backends/backend_tkagg.py,sha256=z9gB16fq2d-DUNpbeSDDLWaYmc0Jz3cDqNlBKhnQg0c,592
matplotlib/backends/backend_tkcairo.py,sha256=JaGGXh8Y5FwVZtgryIucN941Olf_Pn6f4Re7Vuxl1-c,845
matplotlib/backends/backend_webagg.py,sha256=YzNscpsID8yu0XYO3NXiZLMPZnDKMAKdG_TO2Ya3g4k,11022
matplotlib/backends/backend_webagg_core.py,sha256=RZisxLS_pocuBRr5_lqBnF91flQA5upI8PiP8-Zitl0,18748
matplotlib/backends/backend_wx.py,sha256=Y3IM1DEtPTeXOqkyLIGmNuKqDReyHMTtHzqTuwdfPhM,51296
matplotlib/backends/backend_wxagg.py,sha256=tzcwYyW34j4LPfHm9uhuHwepwZIcspi3y8oPC8FJkdk,1468
matplotlib/backends/backend_wxcairo.py,sha256=TK-m3S0c1WipfKE2IpIPNeE4hoXPjfMvnWAzHpCXpFs,848
matplotlib/backends/qt_compat.py,sha256=t_4aD4rg5maItXABLtVG6JIfuQwZnHwhMcjtz6AUwQs,5346
matplotlib/backends/qt_editor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/qt_editor/__pycache__/__init__.cpython-313.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formlayout.cpython-313.pyc,,
matplotlib/backends/qt_editor/__pycache__/figureoptions.cpython-313.pyc,,
matplotlib/backends/qt_editor/_formlayout.py,sha256=QmqqqLO6waqeSGOKjDNUwjvon53Z7yqil5AfqfftDWY,20953
matplotlib/backends/qt_editor/figureoptions.py,sha256=ljd7fCBiE0kk2ATNTaB5Dg8U6Gn7Lz4Vhi3XX-VbXeE,9851
matplotlib/backends/registry.py,sha256=M2BHhBKbUJ8E2B2QExfVf8NbAK4ZFhJtmKRelBZZDh4,15480
matplotlib/backends/web_backend/all_figures.html,sha256=44Y-GvIJbNlqQaKSW3kwVKpTxBSG1WsdYz3ZmYHlUsA,1753
matplotlib/backends/web_backend/css/boilerplate.css,sha256=qui16QXRnQFNJDbcMasfH6KtN9hLjv8883U9cJmsVCE,2310
matplotlib/backends/web_backend/css/fbm.css,sha256=wa4vNkNv7fQ_TufjJjecFZEzPMR6W8x6uXJga_wQILw,1456
matplotlib/backends/web_backend/css/mpl.css,sha256=ruca_aA5kNnP-MZmLkriu8teVP1nIgwcFEpoB16j8Z4,1611
matplotlib/backends/web_backend/css/page.css,sha256=ca3nO3TaPw7865PN5SlGJBTc2H3rBXQCMaFPywX29y4,1623
matplotlib/backends/web_backend/ipython_inline_figure.html,sha256=wgSxUh3xpPAxOnZgSMnrhDM5hYncOfWRGgaCUezvedY,1311
matplotlib/backends/web_backend/js/mpl.js,sha256=FAFf8huEmvymJbTHFUQ5pbQt48NtXmN_DNACP2dCDMw,24432
matplotlib/backends/web_backend/js/mpl_tornado.js,sha256=Zs2Uzs7YUilG765nYvanCo-IK8HkHDtIum1KAq6bQ_w,302
matplotlib/backends/web_backend/js/nbagg_mpl.js,sha256=F-By4ZjOSmwpNAkxUUxUk35qCjGlf0B28Z0aOyfpxDM,9514
matplotlib/backends/web_backend/single_figure.html,sha256=wEBwF602JLHErBUEXiS6jXqmxYAIzHpa3MMFrnev6rs,1357
matplotlib/bezier.py,sha256=ahJ1BRJoTbcxJoNmF1mCYc96mWs5jSM4DOK58jYaXkU,19049
matplotlib/bezier.pyi,sha256=itubQt_U1IKnQ508bhy6J8LSpaspvZIeOmJqg7JvOmU,2586
matplotlib/category.py,sha256=UsZ8rbADAH96YfpjD-N1DLTcS_cQwlszIIQ2ZRaiWm4,7377
matplotlib/cbook.py,sha256=nF2hNC_ryiJcec58Cnp_0mAGSD0LfsnjbzUuCH4p-Os,80020
matplotlib/cbook.pyi,sha256=YkPZz_bsTYQJQ_wKx-qbsJYLhy2t2nCyvLNjJyiRtpw,6037
matplotlib/cm.py,sha256=5xav-BvJ4pWSVdvGql4oYc4EpA-R6OuUP5xjrZCMKvg,10350
matplotlib/cm.pyi,sha256=Ot5j2zDHYTra4kKuTwxYp7X5TsNwg_cJuYa7BxmQz-A,939
matplotlib/collections.py,sha256=5AcX3lTsKD1ObSOkLr-irzYPfdJxsNrgw5dAD886vCE,96311
matplotlib/collections.pyi,sha256=uZ4fPdq34oghdEtS1UkY7P0oOE2oPUzvS0Cr09yktaQ,10775
matplotlib/colorbar.py,sha256=-PwV3xkczcyPq9Ki_zfpQdP_1EqOFBzoeUSsMlUF2cE,60687
matplotlib/colorbar.pyi,sha256=C1NzjMWFKm01rPUUr15Yjz1WB0pGakJQeatJO7G0mNs,4966
matplotlib/colorizer.py,sha256=RzE0zjqB6elpketKidy6n-RRDbYF98speEIOIMfIFb4,25180
matplotlib/colorizer.pyi,sha256=B_5-rEvNo4DblSifLodf3OVVEc3bTGsIYqqaQdU_4Tw,3308
matplotlib/colors.py,sha256=h1YV59y4WvstOPC0x8eZzwHYItal7VjjVcyVS3niaSM,137303
matplotlib/colors.pyi,sha256=XhCi3MtgSozOY5FATtPUHyKO4xMrvi7TvbCAl714Uxc,14908
matplotlib/container.py,sha256=Y6v4j79gMk8QDYfrdOqbbJHH0BoOIO9enz4dtlaBSJU,4565
matplotlib/container.pyi,sha256=DdthHVj1-bQQV3VcpMD1uIPLXVUKyKhWMXy8yCniK1I,1805
matplotlib/contour.py,sha256=wpMtrMZEiDYPRuFbZ592HvQqn4NOBySfM17detKoenY,68391
matplotlib/contour.pyi,sha256=X4DSH0u1YFUvKxuoAv8YbmVOJTJ-wbHig4lznzhsI0U,5300
matplotlib/dates.py,sha256=xfcFY3g3AsW9isnEIKFGj4gXq0Xeoxox_3fxNCDQ01c,66306
matplotlib/dviread.py,sha256=BqQg0fqXD_TmABJPSWDC-H2azt7B53NfOE1SC6mbXiI,42590
matplotlib/dviread.pyi,sha256=5fps3GiPf1ibKY1TH6__kNEiJ9xhpVgzgait6jo09Hc,2139
matplotlib/figure.py,sha256=CNPMl0QCxhr3h3WGcd271pE4Xtv4rapRWuUSFSNJSuc,141862
matplotlib/figure.pyi,sha256=rROP8wvPmeSwTKoJwFaWsCHFpZjfgDk_7HE0wPfZQRg,14914
matplotlib/font_manager.py,sha256=tSpOlZqiNUdIn3gQCFN_-nOLZ08WXBqj_unP6o1JyIw,57651
matplotlib/font_manager.pyi,sha256=Jse1IshOdjhXHdjcvTxtos3rgsuECbRJzc264A-ZGus,5052
matplotlib/ft2font.cp313-win_amd64.pyd,sha256=G1H3GJvr3WEZN4nmOce_9uov73vS9_Kcs7XGVBdsCZw,925696
matplotlib/ft2font.pyi,sha256=yuDEfdw-7VyFuAHts-sMRm4VKV7IxYresBxEQgNOaPM,9253
matplotlib/gridspec.py,sha256=s6RkXEk4ORWsMHjXkK98ZvosZ5D-qgenVNk0hIvMwOw,29786
matplotlib/gridspec.pyi,sha256=3IQi_Q5KdwyPT_J_FWcngJ7iUOOCH4PSm42USm16Ofg,5099
matplotlib/hatch.py,sha256=tfEM0DQxyLkUE87lHduMNlE55GKINtAZo4Y_YsAmSJo,7453
matplotlib/hatch.pyi,sha256=OEFkNe8TgrBli_MDMW99Tvhr44S4ySPaYQomjVXpUUA,2098
matplotlib/image.py,sha256=MLFg06J2xllm1tmgDnlLYw8gTy7__NGnkf4SZP-3mSs,69765
matplotlib/image.pyi,sha256=R88aGGu6wP0lFKRudaM7iNxGgq4kh9uX9QRaZSorTLQ,7066
matplotlib/inset.py,sha256=fJV8VMMY07_97iJR3_RO892WAKE7myfV_Gqg-qK99vg,10154
matplotlib/inset.pyi,sha256=c-IVW14bpqb3qu7bz0EabvNXJnZwJAXEXoQoIcrL8ns,968
matplotlib/layout_engine.py,sha256=mwRNh13mIQ-wpjQVgBaB_LWKN1AiQTWldg-X9nLmVok,11433
matplotlib/layout_engine.pyi,sha256=9cKFTFPCvwmKIygTUvrvOq1iWnFYme9Shniiv2QbC74,1788
matplotlib/legend.py,sha256=5NDPbWqkiIkP1FJ3sLNqmew2jkNjawQf2G_BqZNG_u4,55343
matplotlib/legend.pyi,sha256=hn-MNF3SPHtSUqIhwVXTebU_Nzk_wIh5iKgf7AEAZRg,5364
matplotlib/legend_handler.py,sha256=ekhZTT1X9yQd7TNIRdu_vui5FR2CTmI0NWTFJTqF1N4,29872
matplotlib/legend_handler.pyi,sha256=3VEfeioGIAxhd3mhg4PXETZjCKf4OlXL0jz1MAFGtos,7655
matplotlib/lines.py,sha256=BG3xhTuzYDFjzf-k5CcQc_V7W4HlQl4CL7RCQQz0EC8,57920
matplotlib/lines.pyi,sha256=3tG7tD8GZ8YPphw4nmkgS9SfdLV8icW36bv61jb9RlU,6081
matplotlib/markers.py,sha256=g6ukerZ5n_sD_Enk0eJA4kkTvOOp8AfSGBq60PHot-E,33708
matplotlib/markers.pyi,sha256=FFFBsvilnbd8-5L04U70kfVoyBwc18w_fZ6DysTj9p0,1678
matplotlib/mathtext.py,sha256=yTlpUfnKxzrFl7XNjyuC-xMBBnxUufU6GVwQyhQAI2o,5104
matplotlib/mathtext.pyi,sha256=RCVxYGQ_CJ6wC7v_HkqoouU2PhtcvlJ1ffIyxzAC-so,1045
matplotlib/mlab.py,sha256=T71p4x0KJNVGrwUuoDdgcjWQucIQTuVrocOC98Zdwsc,30210
matplotlib/mlab.pyi,sha256=mkR7wbJS9eCQfCFsUWoXnqrAy0kcE3cVqpFZCeOGC_Q,3583
matplotlib/mpl-data/fonts/afm/cmex10.afm,sha256=blR3ERmrVBV5XKkAnDCj4NMeYVgzH7cXtJ3u59u9GuE,12070
matplotlib/mpl-data/fonts/afm/cmmi10.afm,sha256=5qwEOpedEo76bDUahyuuF1q0cD84tRrX-VQ4p3MlfBo,10416
matplotlib/mpl-data/fonts/afm/cmr10.afm,sha256=WDvgC_D3UkGJg9u-J0U6RaT02lF4oz3lQxHtg1r3lYw,10101
matplotlib/mpl-data/fonts/afm/cmsy10.afm,sha256=AbmzvCVWBceHRfmRfeJ9E6xzOQTFLk0U1zDfpf3_MaM,8295
matplotlib/mpl-data/fonts/afm/cmtt10.afm,sha256=4ji7_mTpeWMa93o_UHBWPKCnqsBfhJJNllat1lJArP4,6501
matplotlib/mpl-data/fonts/afm/pagd8a.afm,sha256=jjFrigwkTpYLqa26cpzZvKQNBo-PuF4bmDVqaM4pMWw,17183
matplotlib/mpl-data/fonts/afm/pagdo8a.afm,sha256=sgNQdeYyx8J-itGw9h31y95aMBiTCRvmNSPTXwwS7xg,17255
matplotlib/mpl-data/fonts/afm/pagk8a.afm,sha256=ZUtfHPloNqcvGMHMxaKDSlshhOcjwheUx143RwpGdIU,17241
matplotlib/mpl-data/fonts/afm/pagko8a.afm,sha256=Yj1wBg6Jsqqz1KBfhRoJ3ACR-CMQol8Fj_ZM5NZ1gDk,17346
matplotlib/mpl-data/fonts/afm/pbkd8a.afm,sha256=Zl5o6J_di9Y5j2EpHtjew-_sfg7-WoeVmO9PzOYSTUc,15157
matplotlib/mpl-data/fonts/afm/pbkdi8a.afm,sha256=JAOno930iTyfZILMf11vWtiaTgrJcPpP6FRTRhEMMD4,15278
matplotlib/mpl-data/fonts/afm/pbkl8a.afm,sha256=UJqJjOJ6xQDgDBLX157mKpohIJFVmHM-N6x2-DiGv14,15000
matplotlib/mpl-data/fonts/afm/pbkli8a.afm,sha256=AWislZ2hDbs0ox_qOWREugsbS8_8lpL48LPMR40qpi0,15181
matplotlib/mpl-data/fonts/afm/pcrb8a.afm,sha256=6j1TS2Uc7DWSc-8l42TGDc1u0Fg8JspeWfxFayjUwi8,15352
matplotlib/mpl-data/fonts/afm/pcrbo8a.afm,sha256=smg3mjl9QaBDtQIt06ko5GvaxLsO9QtTvYANuE5hfG0,15422
matplotlib/mpl-data/fonts/afm/pcrr8a.afm,sha256=7nxFr0Ehz4E5KG_zSE5SZOhxRH8MyfnCbw-7x5wu7tw,15339
matplotlib/mpl-data/fonts/afm/pcrro8a.afm,sha256=NKEz7XtdFkh9cA8MvY-S3UOZlV2Y_J3tMEWFFxj7QSg,15443
matplotlib/mpl-data/fonts/afm/phvb8a.afm,sha256=NAx4M4HjL7vANCJbc-tk04Vkol-T0oaXeQ3T2h-XUvM,17155
matplotlib/mpl-data/fonts/afm/phvb8an.afm,sha256=8e_myD-AQkNF7q9XNLb2m76_lX2TUr3a5wog_LIE1sk,17086
matplotlib/mpl-data/fonts/afm/phvbo8a.afm,sha256=8fkBRmJ-SWY2YrBg8fFyjJyrJp8daQ6JPO6LvhM8xPI,17230
matplotlib/mpl-data/fonts/afm/phvbo8an.afm,sha256=aeVRvV4r15BBvxuRJ0MG8ZHuH2HViuIiCYkvuapmkmM,17195
matplotlib/mpl-data/fonts/afm/phvl8a.afm,sha256=IyMYM-bgl-gI6rG0EuZZ2OLzlxJfGeSh8xqsh0t-eJQ,15627
matplotlib/mpl-data/fonts/afm/phvlo8a.afm,sha256=s12C-eNnIDHJ_UVbuiprjxBjCiHIbS3Y8ORTC-qTpuI,15729
matplotlib/mpl-data/fonts/afm/phvr8a.afm,sha256=Kt8KaRidts89EBIK29X2JomDUEDxvroeaJz_RNTi6r4,17839
matplotlib/mpl-data/fonts/afm/phvr8an.afm,sha256=lL5fAHTRwODl-sB5mH7IfsD1tnnea4yRUK-_Ca2bQHM,17781
matplotlib/mpl-data/fonts/afm/phvro8a.afm,sha256=3KqK3eejiR4hIFBUynuSX_4lMdE2V2T58xOF8lX-fwc,17919
matplotlib/mpl-data/fonts/afm/phvro8an.afm,sha256=Vx9rRf3YfasMY7tz-njSxz67xHKk-fNkN7yBi0X2IP0,17877
matplotlib/mpl-data/fonts/afm/pncb8a.afm,sha256=aoXepTcDQtQa_mspflMJkEFKefzXHoyjz6ioJVI0YNc,16028
matplotlib/mpl-data/fonts/afm/pncbi8a.afm,sha256=pCWW1MYgy0EmvwaYsaYJaAI_LfrsKmDANHu7Pk0RaiU,17496
matplotlib/mpl-data/fonts/afm/pncr8a.afm,sha256=0CIB2BLe9r-6_Wl5ObRTTf98UOrezmGQ8ZOuBX5kLks,16665
matplotlib/mpl-data/fonts/afm/pncri8a.afm,sha256=5R-pLZOnaHNG8pjV6MP3Ai-d2OTQYR_cYCb5zQhzfSU,16920
matplotlib/mpl-data/fonts/afm/pplb8a.afm,sha256=3EzUbNnXr5Ft5eFLY00W9oWu59rHORgDXUuJaOoKN58,15662
matplotlib/mpl-data/fonts/afm/pplbi8a.afm,sha256=X_9tVspvrcMer3OS8qvdwjFFqpAXYZneyCL2NHA902g,15810
matplotlib/mpl-data/fonts/afm/pplr8a.afm,sha256=ijMb497FDJ9nVdVMb21F7W3-cu9sb_9nF0oriFpSn8k,15752
matplotlib/mpl-data/fonts/afm/pplri8a.afm,sha256=8KITbarcUUMi_hdoRLLmNHtlqs0TtOSKqtPFft7X5nY,15733
matplotlib/mpl-data/fonts/afm/psyr.afm,sha256=Iyt8ajE4B2Tm34oBj2pKtctIf9kPfq05suQefq8p3Ro,9644
matplotlib/mpl-data/fonts/afm/ptmb8a.afm,sha256=bL1fA1NC4_nW14Zrnxz4nHlXJb4dzELJPvodqKnYeMg,17983
matplotlib/mpl-data/fonts/afm/ptmbi8a.afm,sha256=-_Ui6XlKaFTHEnkoS_-1GtIr5VtGa3gFQ2ezLOYHs08,18070
matplotlib/mpl-data/fonts/afm/ptmr8a.afm,sha256=IEcsWcmzJyjCwkgsw4o6hIMmzlyXUglJat9s1PZNnEU,17942
matplotlib/mpl-data/fonts/afm/ptmri8a.afm,sha256=49fQMg5fIGguZ7rgc_2styMK55Pv5bPTs7wCzqpcGpk,18068
matplotlib/mpl-data/fonts/afm/putb8a.afm,sha256=qMaHTdpkrNL-m4DWhjpxJCSmgYkCv1qIzLlFfM0rl40,21532
matplotlib/mpl-data/fonts/afm/putbi8a.afm,sha256=g7AVJyiTxeMpNk_1cSfmYgM09uNUfPlZyWGv3D1vcAk,21931
matplotlib/mpl-data/fonts/afm/putr8a.afm,sha256=XYmNC5GQgSVAZKTIYdYeNksE6znNm9GF_0SmQlriqx0,22148
matplotlib/mpl-data/fonts/afm/putri8a.afm,sha256=i7fVe-iLyLtQxCfAa4IxdxH-ufcHmMk7hbCGG5TxAY4,21891
matplotlib/mpl-data/fonts/afm/pzcmi8a.afm,sha256=wyuoIWEZOcoXrSl1tPzLkEahik7kGi91JJj-tkFRG4A,16250
matplotlib/mpl-data/fonts/afm/pzdr.afm,sha256=MyjLAnzKYRdQBfof1W3k_hf30MvqOkqL__G22mQ5xww,9467
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm,sha256=sIDDI-B82VZ3C0mI_mHFITCZ7PVn37AIYMv1CrHX4sE,15333
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm,sha256=zg61QobD3YU9UBfCXmvmhBNaFKno-xj8sY0b2RpgfLw,15399
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm,sha256=vRQm5j1sTUN4hicT1PcVZ9P9DTTUHhEzfPXqUUzVZhE,15441
matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm,sha256=Mdcq2teZEBJrIqVXnsnhee7oZnTs6-P8_292kWGTrw4,15335
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm,sha256=i2l4gcjuYXoXf28uK7yIVwuf0rnw6J7PwPVQeHj5iPw,69269
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm,sha256=Um5O6qK11DXLt8uj_0IoWkc84TKqHK3bObSKUswQqvY,69365
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm,sha256=hVYDg2b52kqtbVeCzmiv25bW1yYdpkZS-LXlGREN2Rs,74392
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm,sha256=23cvKDD7bQAJB3kdjSahJSTZaUOppznlIO6FXGslyW8,74292
matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm,sha256=P5UaoXr4y0qh4SiMa5uqijDT6ZDr2-jPmj1ayry593E,9740
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm,sha256=cQTmr2LFPwKQE_sGQageMcmFicjye16mKJslsJLHQyE,64251
matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm,sha256=pzWOdycm6RqocBWgAVY5Jq0z3Fp7LuqWgLNMx4q6OFw,59642
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm,sha256=bK5puSMpGT_YUILwyJrXoxjfj7XJOdfv5TQ_iKsJRzw,66328
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm,sha256=hhNrUnpazuDDKD1WpraPxqPWCYLrO7D7bMVOg-zI13o,60460
matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm,sha256=ZuOmt9GcKofjdOq8kqhPhtAIhOwkL2rTJTmZxAjFakA,9527
matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt,sha256=MRv8ppSITYYAb7lt5EOw9DWWNZIblfxsFhu5TQE7cpI,828
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf,sha256=sYS4njwQdfIva3FXW2_CDUlys8_TsjMiym_Vltyu8Wc,704128
matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf,sha256=bt8CgxYBhq9FHL7nHnuEXy5Mq_Jku5ks5mjIPCVGXm8,641720
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf,sha256=zN90s1DxH9PdV3TeUOXmNGoaXaH1t9X7g1kGZel6UhM,633840
matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf,sha256=P99pyr8GBJ6nCgC1kZNA4s4ebQKwzDxLRPtoAb0eDSI,756072
matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf,sha256=ggmdz7paqGjN_CdFGYlSX-MpL3N_s8ngMozpzvWWUvY,25712
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf,sha256=uq2ppRcv4giGJRr_BDP8OEYZEtXa8HKH577lZiCo2pY,331536
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf,sha256=ppCBwVx2yCfgonpaf1x0thNchDSZlVSV_6jCDTqYKIs,253116
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf,sha256=KAUoE_enCfyJ9S0ZLcmV708P3Fw9e3OknWhJsZFtDNA,251472
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf,sha256=YC7Ia4lIz82VZIL-ZPlMNshndwFJ7y95HUYT9EO87LM,340240
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf,sha256=w3U_Lta8Zz8VhG3EWt2-s7nIcvMvsY_VOiHxvvHtdnY,355692
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf,sha256=2T7-x6nS6CZ2jRou6VuVhw4V4pWZqE80hK8d4c7C4YE,347064
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf,sha256=PnmU-8VPoQzjNSpC1Uj63X2crbacsRCbydlg9trFfwQ,345612
matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf,sha256=EHJElW6ZYrnpb6zNxVGCXgrgiYrhNzcTPhuSGi_TX_o,379740
matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf,sha256=KRTzLkfHd8J75Wd6-ufbTeefnkXeb8kJfZlJwjwU99U,14300
matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU,sha256=11k43sCY8G8Kw8AIUwZdlPAgvhw8Yu8dwpdboVtNmw4,4816
matplotlib/mpl-data/fonts/ttf/LICENSE_STIX,sha256=urPTHf7wf0g2JPL2XycR52BluOcnMnixwHHt4QQcmVk,5476
matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf,sha256=FnN4Ax4t3cYhbWeBnJJg6aBv_ExHjk4jy5im_USxg8I,448228
matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf,sha256=6FM9xwg_o0a9oZM9YOpKg7Z9CUW86vGzVB-CtKDixqA,237360
matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf,sha256=mHiP1LpI37sr0CbA4gokeosGxzcoeWKLemuw1bsJc2w,181152
matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf,sha256=bPyzM9IrfDxiO9_UAXTxTIXD1nMcphZsHtyAFA6uhSc,175040
matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf,sha256=Ulb34CEzWsSFTRgPDovxmJZOwvyCAXYnbhaqvGU3u1c,59108
matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf,sha256=XRBqW3jR_8MBdFU0ObhiV7-kXwiBIMs7QVClHcT5tgs,30512
matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf,sha256=pb22DnbDf2yQqizotc3wBDqFGC_g27YcCGJivH9-Le8,41272
matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf,sha256=BMr9pWiBv2YIZdq04X4c3CgL6NPLUPrl64aV1N4w9Ug,46752
matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf,sha256=wYuH1gYUpCuusqItRH5kf9p_s6mUD-9X3L5RvRtKSxs,13656
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf,sha256=yNdvjUoSmsZCULmD7SVq9HabndG9P4dPhboL1JpAf0s,12228
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf,sha256=-9xVMYL4_1rcO8FiCKrCfR4PaSmKtA42ddLGqwtei1w,15972
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf,sha256=cYexyo8rZcdqMlpa9fNF5a2IoXLUTZuIvh0JD1Qp0i4,12556
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf,sha256=0lbHzpndzJmO8S42mlkhsz5NbvJLQCaH5Mcc7QZRDzc,19760
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf,sha256=3eBc-VtYbhQU3BnxiypfO6eAzEu8BdDvtIJSFbkS2oY,12192
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf,sha256=XFSKCptbESM8uxHtUFSAV2cybwxhSjd8dWVByq6f3w0,15836
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf,sha256=MUCYHrA0ZqFiSE_PjIGlJZgMuv79aUgQqE7Dtu3kuo0,12116
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf,sha256=_sdxDuEwBDtADpu9CyIXQxV7sIqA2TZVBCUiUjq5UCk,15704
matplotlib/mpl-data/fonts/ttf/cmb10.ttf,sha256=B0SXtQxD6ldZcYFZH5iT04_BKofpUQT1ZX_CSB9hojo,25680
matplotlib/mpl-data/fonts/ttf/cmex10.ttf,sha256=ryjwwXByOsd2pxv6WVrKCemNFa5cPVTOGa_VYZyWqQU,21092
matplotlib/mpl-data/fonts/ttf/cmmi10.ttf,sha256=MJKWW4gR_WpnZXmWZIRRgfwd0TMLk3-RWAjEhdMWI00,32560
matplotlib/mpl-data/fonts/ttf/cmr10.ttf,sha256=Tdl2GwWMAJ25shRfVe5mF9CTwnPdPWxbPkP_YRD6m_Y,26348
matplotlib/mpl-data/fonts/ttf/cmss10.ttf,sha256=ffkag9BbLkcexjjLC0NaNgo8eSsJ_EKn2mfpHy55EVo,20376
matplotlib/mpl-data/fonts/ttf/cmsy10.ttf,sha256=uyJu2TLz8QDNDlL15JEu5VO0G2nnv9uNOFTbDrZgUjI,29396
matplotlib/mpl-data/fonts/ttf/cmtt10.ttf,sha256=YhHwmuk1mZka_alwwkZp2tGnfiU9kVYk-_IS9wLwcdc,28136
matplotlib/mpl-data/images/back-symbolic.svg,sha256=Okj_ressZkfe6Ewv_o7GF5toc5qWCeFkQ2cHQ25BdVE,1532
matplotlib/mpl-data/images/back.pdf,sha256=ZR7CJo_dAeCM-KlaGvskgtHQyRtrPIolc8REOmcoqJk,1623
matplotlib/mpl-data/images/back.png,sha256=E4dGf4Gnz1xJ1v2tMygHV0YNQgShreDeVApaMb-74mU,380
matplotlib/mpl-data/images/back.svg,sha256=Okj_ressZkfe6Ewv_o7GF5toc5qWCeFkQ2cHQ25BdVE,1532
matplotlib/mpl-data/images/back_large.png,sha256=9A6hUSQeszhYONE4ZuH3kvOItM0JfDVu6tkfromCbsQ,620
matplotlib/mpl-data/images/filesave-symbolic.svg,sha256=dMGXvLSOHPu44kiWgZx-B_My_tLWaP6J6GgxJfL4FW0,2049
matplotlib/mpl-data/images/filesave.pdf,sha256=P1EPPV2g50WTt8UaX-6kFoTZM1xVqo6S2H6FJ6Zd1ec,1734
matplotlib/mpl-data/images/filesave.png,sha256=b7ctucrM_F2mG-DycTedG_a_y4pHkx3F-zM7l18GLhk,458
matplotlib/mpl-data/images/filesave.svg,sha256=dMGXvLSOHPu44kiWgZx-B_My_tLWaP6J6GgxJfL4FW0,2049
matplotlib/mpl-data/images/filesave_large.png,sha256=LNbRD5KZ3Kf7nbp-stx_a1_6XfGBSWUfDdpgmnzoRvk,720
matplotlib/mpl-data/images/forward-symbolic.svg,sha256=kOiKq3a4mieMRLVCwQBdOMTRrWG2NOX_5-rbAFHpdmQ,1551
matplotlib/mpl-data/images/forward.pdf,sha256=KIqIL4YId43LkcOxV_TT5uvz1SP8k5iUNUeJmAElMV8,1630
matplotlib/mpl-data/images/forward.png,sha256=pKbLepgGiGeyY2TCBl8svjvm7Z4CS3iysFxcq4GR-wk,357
matplotlib/mpl-data/images/forward.svg,sha256=kOiKq3a4mieMRLVCwQBdOMTRrWG2NOX_5-rbAFHpdmQ,1551
matplotlib/mpl-data/images/forward_large.png,sha256=36h7m7DZDHql6kkdpNPckyi2LKCe_xhhyavWARz_2kQ,593
matplotlib/mpl-data/images/hand.pdf,sha256=hspwkNY915KPD7AMWnVQs7LFPOtlcj0VUiLu76dMabQ,4172
matplotlib/mpl-data/images/hand.png,sha256=2cchRETGKa0hYNKUxnJABwkyYXEBPqJy_VqSPlT0W2Q,979
matplotlib/mpl-data/images/hand.svg,sha256=hxxBtakaVFA7mpZOGakvo0QUcb2x06rojeS5gnVmyuc,4906
matplotlib/mpl-data/images/help-symbolic.svg,sha256=XVcFcuzcL3SQ3LjfSbtdLYDjoB5YUkj2jk2Gk8vaZF8,1890
matplotlib/mpl-data/images/help.pdf,sha256=CeE978IMi0YWznWKjIT1R8IrP4KhZ0S7usPUvreSgcA,1813
matplotlib/mpl-data/images/help.png,sha256=s4pQrqaQ0py8I7vc9hv3BI3DO_tky-7YBMpaHuBDCBY,472
matplotlib/mpl-data/images/help.svg,sha256=XVcFcuzcL3SQ3LjfSbtdLYDjoB5YUkj2jk2Gk8vaZF8,1890
matplotlib/mpl-data/images/help_large.png,sha256=1IwEyWfGRgnoCWM-r9CJHEogTJVD5n1c8LXTK4AJ4RE,747
matplotlib/mpl-data/images/home-symbolic.svg,sha256=ptrus8h5PZTi9ahYfnaz-uZ8MAHCr72aPeMW48TBR9Q,1911
matplotlib/mpl-data/images/home.pdf,sha256=e0e0pI-XRtPmvUCW2VTKL1DeYu1pvPmUUeRSgEbWmik,1737
matplotlib/mpl-data/images/home.png,sha256=IcFdAAUa6_A0qt8IO3I8p4rpXpQgAlJ8ndBECCh7C1w,468
matplotlib/mpl-data/images/home.svg,sha256=ptrus8h5PZTi9ahYfnaz-uZ8MAHCr72aPeMW48TBR9Q,1911
matplotlib/mpl-data/images/home_large.png,sha256=uxS2O3tWOHh1iau7CaVV4ermIJaZ007ibm5Z3i8kXYg,790
matplotlib/mpl-data/images/matplotlib.pdf,sha256=BkSUf-2xoij-eXfpV2t7y1JFKG1zD1gtV6aAg3Xi_wE,22852
matplotlib/mpl-data/images/matplotlib.png,sha256=w8KLRYVa-voUZXa41hgJauQuoois23f3NFfdc72pUYY,1283
matplotlib/mpl-data/images/matplotlib.svg,sha256=QiTIcqlQwGaVPtHsEk-vtmJk1wxwZSvijhqBe_b9VCI,62087
matplotlib/mpl-data/images/matplotlib_large.png,sha256=ElRoue9grUqkZXJngk-nvh4GKfpvJ4gE69WryjCbX5U,3088
matplotlib/mpl-data/images/move-symbolic.svg,sha256=_uamLnjQ20iwSuKbd8JvTXUFaRq4206MrpFWvtErr8I,2529
matplotlib/mpl-data/images/move.pdf,sha256=CXk3PGK9WL5t-5J-G2X5Tl-nb6lcErTBS5oUj2St6aU,1867
matplotlib/mpl-data/images/move.png,sha256=TmjR41IzSzxGbhiUcV64X0zx2BjrxbWH3cSKvnG2vzc,481
matplotlib/mpl-data/images/move.svg,sha256=_uamLnjQ20iwSuKbd8JvTXUFaRq4206MrpFWvtErr8I,2529
matplotlib/mpl-data/images/move_large.png,sha256=Skjz2nW_RTA5s_0g88gdq2hrVbm6DOcfYW4Fu42Fn9U,767
matplotlib/mpl-data/images/qt4_editor_options.pdf,sha256=2qu6GVyBrJvVHxychQoJUiXPYxBylbH2j90QnytXs_w,1568
matplotlib/mpl-data/images/qt4_editor_options.png,sha256=EryQjQ5hh2dwmIxtzCFiMN1U6Tnd11p1CDfgH5ZHjNM,380
matplotlib/mpl-data/images/qt4_editor_options.svg,sha256=sdrNIxYT-BLvJ30ASnaRQ5PxF3SB41-pgdaIJT0KqBg,1264
matplotlib/mpl-data/images/qt4_editor_options_large.png,sha256=-Pd-9Vh5aIr3PZa8O6Ge_BLo41kiEnpmkdDj8a11JkY,619
matplotlib/mpl-data/images/subplots-symbolic.svg,sha256=Gq4fDSS99Rv5rbR8_nenV6jcY5VsKPARWeH-BZBk9CU,2150
matplotlib/mpl-data/images/subplots.pdf,sha256=Q0syPMI5EvtgM-CE-YXKOkL9eFUAZnj_X2Ihoj6R4p4,1714
matplotlib/mpl-data/images/subplots.png,sha256=MUfCItq3_yzb9yRieGOglpn0Y74h8IA7m5i70B63iRc,445
matplotlib/mpl-data/images/subplots.svg,sha256=Gq4fDSS99Rv5rbR8_nenV6jcY5VsKPARWeH-BZBk9CU,2150
matplotlib/mpl-data/images/subplots_large.png,sha256=Edu9SwVMQEXJZ5ogU5cyW7VLcwXJdhdf-EtxxmxdkIs,662
matplotlib/mpl-data/images/zoom_to_rect-symbolic.svg,sha256=uMmdGkO43ZHlezkpieR3_MiqlEc5vROffRDOhY4sxm4,1499
matplotlib/mpl-data/images/zoom_to_rect.pdf,sha256=SEvPc24gfZRpl-dHv7nx8KkxPyU66Kq4zgQTvGFm9KA,1609
matplotlib/mpl-data/images/zoom_to_rect.png,sha256=aNz3QZBrIgxu9E-fFfaQweCVNitGuDUFoC27e5NU2L4,530
matplotlib/mpl-data/images/zoom_to_rect.svg,sha256=uMmdGkO43ZHlezkpieR3_MiqlEc5vROffRDOhY4sxm4,1499
matplotlib/mpl-data/images/zoom_to_rect_large.png,sha256=V6pkxmm6VwFExdg_PEJWdK37HB7k3cE_corLa7RbUMk,1016
matplotlib/mpl-data/kpsewhich.lua,sha256=RdyYaBnBLy3NsB5c2R5FGrKu-V-WBcZim24NWilsTfw,139
matplotlib/mpl-data/matplotlibrc,sha256=2hOw4G5YMRexNcWmcT_dogowmoui_AfYOfVvGM6aPHM,44226
matplotlib/mpl-data/plot_directive/plot_directive.css,sha256=utSJ1oETz0UG6AC9hU134J_JY78ENijqMZXN0JMBUfk,318
matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png,sha256=XnKGiCanpDKalQ5anvo5NZSAeDP7fyflzQAaivuc0IE,13634
matplotlib/mpl-data/sample_data/README.txt,sha256=ABz19VBKfGewdY39QInG9Qccgn1MTYV3bT5Ph7TCy2Y,128
matplotlib/mpl-data/sample_data/Stocks.csv,sha256=72878aZNXGxd5wLvFUw_rnj-nfg4gqtrucZji-w830c,67924
matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy,sha256=DpWZ9udAh6ospYqneEa27D6EkRgORFwHosacZXVu98U,1880
matplotlib/mpl-data/sample_data/data_x_x2_x3.csv,sha256=A0SU3buOUGhT-NI_6LQ6p70fFSIU3iLFdgzvzrKR6SE,132
matplotlib/mpl-data/sample_data/eeg.dat,sha256=KGVjFt8ABKz7p6XZirNfcxSTOpGGNuyA8JYErRKLRBc,25600
matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc,sha256=cUqVw5vDHNSZoaO4J0ebZUf5SrJP36775abs7R9Bclg,2186
matplotlib/mpl-data/sample_data/goog.npz,sha256=QAkXzzDmtmT3sNqT18dFhg06qQCNqLfxYNLdEuajGLE,22845
matplotlib/mpl-data/sample_data/grace_hopper.jpg,sha256=qMptc0dlcDsJcoq0f-WfRz2Trjln_CTHwCiMPHrbcTA,61306
matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz,sha256=1JP1CjPoKkQgSUxU0fyhU50Xe9wnqxkLxf5ukvYvtjc,174061
matplotlib/mpl-data/sample_data/logo2.png,sha256=DXNx4FXeyqxHy26AmvNELpwezQLxweLQY9HP7ktKIdc,22279
matplotlib/mpl-data/sample_data/membrane.dat,sha256=q3lbQpIBpbtXXGNw1eFwkN_PwxdDGqk4L46IE2b0M1c,48000
matplotlib/mpl-data/sample_data/msft.csv,sha256=GArKb0O3DgKZRsKdJf6lX3rMSf-PCekIiBoLNdgF7Mk,3211
matplotlib/mpl-data/sample_data/s1045.ima.gz,sha256=MrQk1k9it-ccsk0p_VOTitVmTWCAVaZ6srKvQ2n4uJ4,33229
matplotlib/mpl-data/sample_data/topobathy.npz,sha256=AkTgMpFwLfRQJNy1ysvE89TLMNct-n_TccSsYcQrT78,45224
matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle,sha256=aytOm4eT_SPvs7HC28ZY4GukeN44q-SE0JEMCR8kVOk,1257
matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle,sha256=iopHpMaM3im_AK2aiHGuM2DKM5i9Kc84v6NQEoSb10Q,167
matplotlib/mpl-data/stylelib/_mpl-gallery-nogrid.mplstyle,sha256=1VOL3USqD6iuGQaSynNg1QhyUwvKLnkLyUKdbBMnnqg,489
matplotlib/mpl-data/stylelib/_mpl-gallery.mplstyle,sha256=MN-q59CiDqHXB8xFKXxzCbJJbJmNDhBe9lDJJAoMTPA,504
matplotlib/mpl-data/stylelib/bmh.mplstyle,sha256=-KbhaI859BITHIoyUZIfpQDjfckgLAlDAS_ydKsm6mc,712
matplotlib/mpl-data/stylelib/classic.mplstyle,sha256=1o5b47VD_RIZv3unnG9Gm2tbprTvOeNGXM8hJCmGuYI,24670
matplotlib/mpl-data/stylelib/dark_background.mplstyle,sha256=GzSBD06jvlRYOqu7D5Z5a5x25l9JnTgtObn7S4D9zug,607
matplotlib/mpl-data/stylelib/fast.mplstyle,sha256=yTa2YEIIP9xi5V_G0p2vSlxghuhNwjRi9gPECMxyRiM,288
matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle,sha256=IcBt2DSDz9CtuyCis9JZtT3Nqoh6LVO-dB66AxObUcA,780
matplotlib/mpl-data/stylelib/ggplot.mplstyle,sha256=u2oPHMLWFtZcpIjHk2swi2Nrt4NgnEtof5lxcwM0RD0,956
matplotlib/mpl-data/stylelib/grayscale.mplstyle,sha256=KCLg-pXpns9cnKDXKN2WH6mV41OH-6cbT-5zKQotSdw,526
matplotlib/mpl-data/stylelib/petroff10.mplstyle,sha256=Qj7pPbHh3L25kdsPt1ypwvKR3dPzuOrFzzQTP3Mfilg,298
matplotlib/mpl-data/stylelib/seaborn-v0_8-bright.mplstyle,sha256=pDqn3-NUyVLvlfkYs8n8HzNZvmslVMChkeH-HtZuJIc,144
matplotlib/mpl-data/stylelib/seaborn-v0_8-colorblind.mplstyle,sha256=eCSzFj5_2vR6n5qu1rHE46wvSVGZcdVqz85ov40ZsH8,148
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark-palette.mplstyle,sha256=p5ABKNQHRG7bk4HXqMQrRBjDlxGAo3RCXHdQmP7g-Ng,142
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark.mplstyle,sha256=I4xQ75vE5_9X4k0cNDiqhhnF3OcrZ2xlPX8Ll7OCkoE,667
matplotlib/mpl-data/stylelib/seaborn-v0_8-darkgrid.mplstyle,sha256=2bXOSzS5gmPzRBrRmzVWyhg_7ZaBRQ6t_-O-cRuyZoA,670
matplotlib/mpl-data/stylelib/seaborn-v0_8-deep.mplstyle,sha256=44dLcXjjRgR-6yaopgGRInaVgz3jk8VJVQTbBIcxRB0,142
matplotlib/mpl-data/stylelib/seaborn-v0_8-muted.mplstyle,sha256=T4o3jvqKD_ImXDkp66XFOV_xrBVFUolJU34JDFk1Xkk,143
matplotlib/mpl-data/stylelib/seaborn-v0_8-notebook.mplstyle,sha256=PcvZQbYrDdducrNlavBPmQ1g2minio_9GkUUFRdgtoM,382
matplotlib/mpl-data/stylelib/seaborn-v0_8-paper.mplstyle,sha256=n0mboUp2C4Usq2j6tNWcu4TZ_YT4-kKgrYO0t-rz1yw,393
matplotlib/mpl-data/stylelib/seaborn-v0_8-pastel.mplstyle,sha256=8nV8qRpbUrnFZeyE6VcQ1oRuZPLil2W74M2U37DNMOE,144
matplotlib/mpl-data/stylelib/seaborn-v0_8-poster.mplstyle,sha256=dUaKqTE4MRfUq2rWVXbbou7kzD7Z9PE9Ko8aXLza8JA,403
matplotlib/mpl-data/stylelib/seaborn-v0_8-talk.mplstyle,sha256=7FnBaBEdWBbncTm6_ER-EQVa_bZgU7dncgez-ez8R74,403
matplotlib/mpl-data/stylelib/seaborn-v0_8-ticks.mplstyle,sha256=CITZmZFUFp40MK2Oz8tI8a7WRoCizQU9Z4J172YWfWw,665
matplotlib/mpl-data/stylelib/seaborn-v0_8-white.mplstyle,sha256=WjJ6LEU6rlCwUugToawciAbKP9oERFHr9rfFlUrdTx0,665
matplotlib/mpl-data/stylelib/seaborn-v0_8-whitegrid.mplstyle,sha256=ec4BjsNzmOvHptcJ3mdPxULF3S1_U1EUocuqfIpw-Nk,664
matplotlib/mpl-data/stylelib/seaborn-v0_8.mplstyle,sha256=_Xu6qXKzi4b3GymCOB1b1-ykKTQ8xhDliZ8ezHGTiAs,1130
matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle,sha256=BsirZVd1LmPWT4tBIz6loZPjZcInoQrIGfC7rvzqmJw,190
matplotlib/offsetbox.py,sha256=hcBN7g2UBFLu_WhBg_bDlnHcIdjc6Pj-kHicfhOpTpI,54490
matplotlib/offsetbox.pyi,sha256=ISf7gRrOhvefNKfutbHIPh6QYS0kAgtA97bYa6N-82o,9909
matplotlib/patches.py,sha256=uXew19bSegJ1vxVIhZMhmf8f4XXyB7wKIwgAnFPfwnk,165064
matplotlib/patches.pyi,sha256=d-sCqVXw_ANsvoUpliH7dllMbTy7TcyRm2IV97Ll0nM,22649
matplotlib/path.py,sha256=QiQRSMjHbKCBpjWyJ3KPIJgK1Eq07qXUYjULaDHgt5g,43269
matplotlib/path.pyi,sha256=VfIuc2KpkcB28uzRjLvkC3Hq5HwH4Zp0Uyj9H9bwcWU,4808
matplotlib/patheffects.py,sha256=4paQgPGKc_xdBz_BZ_MaVhKJ2FxZDGeh5zHwzn3evDY,18387
matplotlib/patheffects.pyi,sha256=7-FhuxGGrer94GtJ1sZ0YxOmK6Nv4oixTmsKb6-ijOg,3664
matplotlib/projections/__init__.py,sha256=gICKgNfjJ-tioWEFa75BKKJZG6JJjx46_RpEqt5fk94,4438
matplotlib/projections/__init__.pyi,sha256=D28dSYmwZcSBFBtNDer-QqE_lqXAhHKersuAlvi89jE,673
matplotlib/projections/__pycache__/__init__.cpython-313.pyc,,
matplotlib/projections/__pycache__/geo.cpython-313.pyc,,
matplotlib/projections/__pycache__/polar.cpython-313.pyc,,
matplotlib/projections/geo.py,sha256=feR2dybCylhk6xe-MzxBr_e8MdbpVc8tBtCQQJ0V2co,17605
matplotlib/projections/geo.pyi,sha256=vPfhvj7_e0ZnKjyfDUNC89RGCktycJBPnn5D8w0A7N8,3775
matplotlib/projections/polar.py,sha256=vEy7FB4FnEu7COWyjZT-zYDt4tbTwkW8BvZCr7kqYiQ,57241
matplotlib/projections/polar.pyi,sha256=IWNSLQIo5cXFBJur5VFVDQBwAtq5n1XFZMnrj85mCBg,6636
matplotlib/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/pylab.py,sha256=VqUqd2J2-dKtltZtsYP8ePKoX1jsNDpTyqlcmpfC-lo,2369
matplotlib/pyplot.py,sha256=Dbp8GKxM6B5oCmcjb8lsik2R8QPtPt3JgmgaqeWqkPw,149704
matplotlib/quiver.py,sha256=L8O6Ke6oH60R9VA_gd9JiA5Fvv5sMPIFyC_uNRrZSo0,48675
matplotlib/quiver.pyi,sha256=Kq1FvQP-DRX1N9Xlp6XF6OJfKkJ7tc4dYq4CEndxEzg,5640
matplotlib/rcsetup.py,sha256=87KrQeDFg4sRu4obxF4mc0eGSCuClGyuzXsxoZ_kme4,51606
matplotlib/rcsetup.pyi,sha256=0VTIhzfKgBcKxOeOdoKq1oa7ZubalOj9Ks0efE0-cms,4337
matplotlib/sankey.py,sha256=eKA9DrX96-bpoWRdeNnvC73aJfPtCnB78WmMPCXQ9rc,36151
matplotlib/sankey.pyi,sha256=P2AGkhdg3ZLqXaQdf8yodblrBpdgEQZBLKPL1mbxmFc,1451
matplotlib/scale.py,sha256=n3JqLU_d3dAKrP1uO5lTipVEaxgiLhDR2sSk8TAXE_M,26924
matplotlib/scale.pyi,sha256=-ptRptcqiAuzfKwrjgSWWOxFmjRUTOKGwIoWtuBXKgY,5057
matplotlib/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/sphinxext/__pycache__/__init__.cpython-313.pyc,,
matplotlib/sphinxext/__pycache__/figmpl_directive.cpython-313.pyc,,
matplotlib/sphinxext/__pycache__/mathmpl.cpython-313.pyc,,
matplotlib/sphinxext/__pycache__/plot_directive.cpython-313.pyc,,
matplotlib/sphinxext/__pycache__/roles.cpython-313.pyc,,
matplotlib/sphinxext/figmpl_directive.py,sha256=rgoJcGRfLdp4jiOlyBIwG83wcyfzbFVfrlrSCmDHL9g,9308
matplotlib/sphinxext/mathmpl.py,sha256=CKIocBSV4prRMPR50NL6WxBTULBa1zReoqo7ZtbvCsc,7871
matplotlib/sphinxext/plot_directive.py,sha256=6GEOX2y0K1Izc98Aojt8pCXS21Ucru6E-T_33lRU92s,32542
matplotlib/sphinxext/roles.py,sha256=U7P4t06foLF2PsZJrTN4mfaspKGRoYwFZ3ijIhIUVyo,4882
matplotlib/spines.py,sha256=_9Dc4oIAgDs3MA6rZCwr1scPGCSzbAnmYp3PQzpKFeU,21923
matplotlib/spines.pyi,sha256=eceCS47bOawgkWdLUMIPt6XX_H-EtWS2t-euz5Bgkbg,2951
matplotlib/stackplot.py,sha256=a6IUAujDjGpaFqiTzGr85eDD8l6baWLPrM5YNvb6g9I,4997
matplotlib/stackplot.pyi,sha256=BX2g3-6dJ4RhjjIguo-gwWhvKZNYqFCQYVw5SKvpM4c,561
matplotlib/streamplot.py,sha256=rXVKYEi4MfSg3HnAaWb8w4qFp95mRf6dosMVqtXZEpE,24011
matplotlib/streamplot.pyi,sha256=fkdkew8u807qd7WXFd_ZjF7FUbx8okOI8A7u4x_ktvg,2690
matplotlib/style/__init__.py,sha256=fraQtyBC3TY2ZTsLdxL7zNs9tJYZtje7tbiqEf3M56M,140
matplotlib/style/__pycache__/__init__.cpython-313.pyc,,
matplotlib/style/__pycache__/core.cpython-313.pyc,,
matplotlib/style/core.py,sha256=RSNH2NO0xK_Y37dbpMWyaWMMjvcMa_NHaKXDa5I4_tA,8362
matplotlib/style/core.pyi,sha256=mIlyz6eChdMjZ_A7S05iJgcWwrWE2NXg2rhm1C1eYlQ,521
matplotlib/table.py,sha256=MkF9s8u2X-9_Cei9sJixljb2_I_B_4UOqVx0iReKIU0,27744
matplotlib/table.pyi,sha256=tcR40hoCWCRLlL-8PtFMa7P5sq8qajDSMFxd25Q90z4,3098
matplotlib/testing/__init__.py,sha256=pTaqH1qgGNBeWCmvK_vjqFJXJ9dvZXCaKQ-8jHSpaEc,6942
matplotlib/testing/__init__.pyi,sha256=ffqfetWzyCVrSx7BlnoxCmbgIaZg2x57nDrq9eucRk0,1752
matplotlib/testing/__pycache__/__init__.cpython-313.pyc,,
matplotlib/testing/__pycache__/_markers.cpython-313.pyc,,
matplotlib/testing/__pycache__/compare.cpython-313.pyc,,
matplotlib/testing/__pycache__/conftest.cpython-313.pyc,,
matplotlib/testing/__pycache__/decorators.cpython-313.pyc,,
matplotlib/testing/__pycache__/exceptions.cpython-313.pyc,,
matplotlib/testing/__pycache__/widgets.cpython-313.pyc,,
matplotlib/testing/_markers.py,sha256=0iNyOi25XLv_gTfSUqiRizdSqJzozePPBMRo72H2Je4,1419
matplotlib/testing/compare.py,sha256=8dVcuP7FauHY1JZQXBD7fNBfQKb4IoXM6pVeq6zJgBs,19780
matplotlib/testing/compare.pyi,sha256=xlJ4chgXKe567NUavlu-dalyPr4wAQUbd2Fz6aK_JII,1192
matplotlib/testing/conftest.py,sha256=CcwfUtIkUOgGBlXXNplQE6dg2U_UBKdH6AajROPJ5uw,6683
matplotlib/testing/conftest.pyi,sha256=zOF_MM2GjCkNi9nZ8vldV9VI41bD67nVhrIgLVuMNsk,416
matplotlib/testing/decorators.py,sha256=1Xiyb2exeN0nDEO4he8MW_VKwFE34cB7EtRl6bcGRzg,18022
matplotlib/testing/decorators.pyi,sha256=0fSpdLBtEH7ZP_trVJ7RPxNtOX9sJ_z-MkNsbUxF8nM,872
matplotlib/testing/exceptions.py,sha256=72QmjiHG7DwxSvlJf8mei-hRit5AH3NKh0-osBo4YbY,138
matplotlib/testing/jpl_units/Duration.py,sha256=9FMBu9uj6orCWtf23cf6_9HCFUC50xAHrCzaxATwQfM,3966
matplotlib/testing/jpl_units/Epoch.py,sha256=-FGxeq-VvCS9GVPwOEE5ind_G4Tl9ztD-gYcW9CWzjo,6100
matplotlib/testing/jpl_units/EpochConverter.py,sha256=fhWjyP567bzcTU_oNuJJpucoolqS88Nt-yEFg1-3yEk,2944
matplotlib/testing/jpl_units/StrConverter.py,sha256=codGw9b_Zc-MG_YK4CiyMrnMR8ahR9hw836O2SsV8QI,2865
matplotlib/testing/jpl_units/UnitDbl.py,sha256=EABjyEK4MVouyvlwi_9KdYDg-qbYY3aLHoUjRw37Fb0,5882
matplotlib/testing/jpl_units/UnitDblConverter.py,sha256=B8DssrQVyC4mwvSFP78cGL0vCnZgVhDaAbZE-jsXLUg,2828
matplotlib/testing/jpl_units/UnitDblFormatter.py,sha256=246hgA4_pCfJm-P94hEsxqnTS9t0XlvLC8p1v_bw2pU,657
matplotlib/testing/jpl_units/__init__.py,sha256=p__9RUwrt2LJ2eoT2JPM-42XLxSJrfA4az3rN5uP6d4,2684
matplotlib/testing/jpl_units/__pycache__/Duration.cpython-313.pyc,,
matplotlib/testing/jpl_units/__pycache__/Epoch.cpython-313.pyc,,
matplotlib/testing/jpl_units/__pycache__/EpochConverter.cpython-313.pyc,,
matplotlib/testing/jpl_units/__pycache__/StrConverter.cpython-313.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDbl.cpython-313.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblConverter.cpython-313.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblFormatter.cpython-313.pyc,,
matplotlib/testing/jpl_units/__pycache__/__init__.cpython-313.pyc,,
matplotlib/testing/widgets.py,sha256=IaoPHgDguJSHd7t97kSVWUZvqTQ-Tsr9tU9kfnmJrAM,3480
matplotlib/testing/widgets.pyi,sha256=Ioau7Q2aPRDZLx8hze2DOe3E1vn7QPxePC74WMR7tFc,831
matplotlib/tests/__init__.py,sha256=XyXveEAxafB87gnbx0jkC0MggzKO8FvORq_6RtJRwo4,366
matplotlib/tests/__pycache__/__init__.cpython-313.pyc,,
matplotlib/tests/__pycache__/conftest.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_afm.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_agg.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_agg_filter.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_animation.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_api.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_arrow_patches.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_artist.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_axes.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_axis.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_bases.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_cairo.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_gtk3.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_inline.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_macosx.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_nbagg.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_pdf.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_pgf.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_ps.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_qt.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_registry.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_svg.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_template.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_tk.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_tools.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backend_webagg.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_backends_interactive.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_basic.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_bbox_tight.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_bezier.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_category.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_cbook.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_collections.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_colorbar.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_colors.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_compare_images.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_constrainedlayout.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_container.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_contour.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_cycles.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_dates.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_datetime.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_determinism.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_doc.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_dviread.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_figure.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_font_manager.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_fontconfig_pattern.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_ft2font.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_getattr.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_gridspec.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_image.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_legend.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_lines.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_marker.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_mathtext.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_matplotlib.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_mlab.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_multivariate_colormaps.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_offsetbox.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_patches.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_path.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_patheffects.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_pickle.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_png.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_polar.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_preprocess_data.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_pyplot.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_quiver.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_rcparams.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_sankey.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_scale.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_simplification.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_skew.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_sphinxext.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_spines.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_streamplot.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_style.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_subplots.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_table.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_testing.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_texmanager.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_text.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_textpath.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_ticker.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_tightlayout.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_transforms.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_triangulation.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_type1font.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_units.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_usetex.cpython-313.pyc,,
matplotlib/tests/__pycache__/test_widgets.cpython-313.pyc,,
matplotlib/tests/conftest.py,sha256=0cKxWVUC25UfQJQcAjFJa73MQE9eSYqKSXGDx7kAq48,138
matplotlib/tests/test_afm.py,sha256=A7jm2o-QaQH9SSpiFxGtZkbVU0LJZE69jfPv7RczOD4,3701
matplotlib/tests/test_agg.py,sha256=xAKrTzvT1dNM_49efiWisnGNrJtINOBenYSOtaRXvIg,10884
matplotlib/tests/test_agg_filter.py,sha256=3c_Smtb4OHEOfdMFCOb2qKhzMXSbNoaUtsJ0pW47Q44,1067
matplotlib/tests/test_animation.py,sha256=z0FJ0gx15rJB_khEPSg1E-vLGQyTj85Ki-uTjFsMEPE,18357
matplotlib/tests/test_api.py,sha256=UJU_qoi7Y8Rbz1S89NYKvOkNwv_RBVPNFajrcXixNds,5736
matplotlib/tests/test_arrow_patches.py,sha256=qgbS9OrlHa4TJihAd2ekW4AupCVgKDd2B5YyF704Pak,6577
matplotlib/tests/test_artist.py,sha256=oblCTkCPZzufghCp19AKwIJftV69tZwEOjeRLn8soAQ,18599
matplotlib/tests/test_axes.py,sha256=PbsmnNA9f3GRtSjCEf4HUvZ5onrBEUIASd1bcalhil8,329934
matplotlib/tests/test_axis.py,sha256=Ljc_Yc7dAFkTuVW0PD0i3vEnjkGOuAehkpcNpWg9A7Y,1446
matplotlib/tests/test_backend_bases.py,sha256=FwUgearYkC7UgqpRpaKk51o5F62ipDL4kUvFROwGVLo,22775
matplotlib/tests/test_backend_cairo.py,sha256=O2LTYjsfPn__bKtTz4MGGBodpSshoPkzu0INsc18xmI,1821
matplotlib/tests/test_backend_gtk3.py,sha256=Zb29s85nbRzwLVmChCA5_LphAeC_yKyP45kCcHxfgPE,2849
matplotlib/tests/test_backend_inline.py,sha256=b7GrmMCrJfI5kXGJKbxdMM9kWBd-JKUdCkRq-vk8JXo,1608
matplotlib/tests/test_backend_macosx.py,sha256=s4Kd1fnwYCuamGRLtkOAYtzer7jVOjLaA2LH52hhPqM,2233
matplotlib/tests/test_backend_nbagg.py,sha256=o6ON7tt_LOW7wzg4StIMYuTgMFSPNWt1FOv2FFMIefk,1459
matplotlib/tests/test_backend_pdf.py,sha256=CxVYTlz8_AQ7o90yh5cavpSnNBLozIL8UhscRYShUVM,14602
matplotlib/tests/test_backend_pgf.py,sha256=WlGdZThz5uQ-x8-HqpMcB5IWmYijflNCC-rVNgvz0yw,13028
matplotlib/tests/test_backend_ps.py,sha256=veHbm4_dpBDcczgomEjQxdz-Sv8wHij3LVSKWST9Bd0,12600
matplotlib/tests/test_backend_qt.py,sha256=yiXBEbzY9KBWL_cG8o_J-q3AVU-LYTF0znKvwX-3szQ,12878
matplotlib/tests/test_backend_registry.py,sha256=SiyyRRMT1tIXKxwn7IXq0SaIrYt3DEvBBe8TvNxa644,5950
matplotlib/tests/test_backend_svg.py,sha256=L73hpBa1P-y978Y3Gpd_eSJAFJMvUvrOqiYaJ-CkQUw,22930
matplotlib/tests/test_backend_template.py,sha256=uuE7oZ9pSBVDWrPL05B0WgCFsgv6HlXyetuPTfJn6a8,2184
matplotlib/tests/test_backend_tk.py,sha256=A1h3AfPhEMbG5PLdDr1gbnwt_DlaQOBgFux5QsIpCpM,8850
matplotlib/tests/test_backend_tools.py,sha256=C-B7NCkyWsQ5KzQEnI5Be16DsAHHZJU9P5v9--wsF-o,501
matplotlib/tests/test_backend_webagg.py,sha256=4Oc-z7w-mTGETB1x0FQ_gZP9qHfyWh5lwWc9qPkkisc,938
matplotlib/tests/test_backends_interactive.py,sha256=oo-vRlDTrLQZOm53FfWECF_JSKDDBJ-BKigekZIO8XE,28880
matplotlib/tests/test_basic.py,sha256=ubAnlE-lFQzMhoBlWYZJKCAsox9Y3jBXS_IMn29Zi84,1141
matplotlib/tests/test_bbox_tight.py,sha256=5yo6yy3HTUIf2-oGZllieaLynmOupzWcuPpeGmgS5BQ,6314
matplotlib/tests/test_bezier.py,sha256=IrrDWAV5O6ELMdziaaOeBI7LfyEaTBVsT4B1T9KMzD8,692
matplotlib/tests/test_category.py,sha256=jWXOAGOR_gpvrjarvJB6Tyot0yOIWaO6d1F9mrNcVPo,12043
matplotlib/tests/test_cbook.py,sha256=xESpD9VE9dXInGp_QyzRCeGvmsgY2j9IyNMSUBae-WA,33656
matplotlib/tests/test_collections.py,sha256=C4jbvAq9t7wcsXY-0SH666aP2t8w4dyO8nRnu7TRdYc,48896
matplotlib/tests/test_colorbar.py,sha256=tDdmWz_LxCbD80ksM6iqrHYqpombyL8QtqpKQSKOS14,46711
matplotlib/tests/test_colors.py,sha256=-Q5VZfQWI-NSMOa4tusxDgWe705a7uymMHLI8h10C8A,61215
matplotlib/tests/test_compare_images.py,sha256=NcBoT8HAxtzoR1zZBu0V3MFpGWtdFaDblQ8o3OTO6zM,3260
matplotlib/tests/test_constrainedlayout.py,sha256=Mx86XCAfw0XEG67dU73GjZZZi38EZMJErwvgrm7-ThU,24270
matplotlib/tests/test_container.py,sha256=FTewckOd3dJqLOzEUa29Itjqusk7Mx7MK5xPVMhzMmc,694
matplotlib/tests/test_contour.py,sha256=O_VIhjDgsb0A-brYR-gJ-WUcqzYzH0XLQD4aI_5SocM,30289
matplotlib/tests/test_cycles.py,sha256=3KyRmWH29WUgvIXUT06tKVDNCfDWqxuxlLueIp-FIl0,5996
matplotlib/tests/test_dates.py,sha256=jOK-KIAMBRCcnlgpzknpLJb3JvO3xFa5Qe3O5mVTeGM,56533
matplotlib/tests/test_datetime.py,sha256=n2TYVItNQcUFBLUivuTXBnc0fElIeSmy9hu4ZNMVurc,32635
matplotlib/tests/test_determinism.py,sha256=harG-hnOpD5HZFKPtHebT0snHu8V6C6BScVFml1Mqyg,7958
matplotlib/tests/test_doc.py,sha256=K6HhdRcHRUNNC0iIhxBLCq5tOqxqHBSvanHxXg5w5iI,1015
matplotlib/tests/test_dviread.py,sha256=JeTuA2FMUj1FddxDVBXUtnvZYTgztE-CyRXL_mI20P0,2764
matplotlib/tests/test_figure.py,sha256=bGyAW9fwJc3gVVul_Vd5f8TEmbrEoDAMWV4Rm5-QXQQ,60289
matplotlib/tests/test_font_manager.py,sha256=Qkewu4tMylgVciamVwWL8TFoDon8YyzN-9po6usN6Mk,14136
matplotlib/tests/test_fontconfig_pattern.py,sha256=LSR6lWF_0cVOshkGiflYaTCLcISRIJM6mjXm5QtDjX4,2168
matplotlib/tests/test_ft2font.py,sha256=lz13lRGmxdQ9eRjQlnigdfQWBwaST1A3_AgEAR5dJA0,40838
matplotlib/tests/test_getattr.py,sha256=Tl_H1zpwLdSIVutc4vi-QwDCeWPzBGpN31N9ItzTkeQ,1090
matplotlib/tests/test_gridspec.py,sha256=SYJuJucA_PyQ2Rsov1RaNhadOEWGDcMbQcVFrWIoy3I,1580
matplotlib/tests/test_image.py,sha256=O9pUxNrGyewPievlC-WDhJ3hkCTsiAq4id6E6hJ22Is,59562
matplotlib/tests/test_legend.py,sha256=QeMjTc20_IZeVShlxEZQQsv2suFAPqpvD86TBHESyks,55122
matplotlib/tests/test_lines.py,sha256=KElNi2GlJCtGLhNpf8rl8bkDhMcQ9ittyRdsX4rdSxo,15035
matplotlib/tests/test_marker.py,sha256=w0WVHoaD-6iybjUSENoVFFdUOOR13943JcE4sgz3qhI,11410
matplotlib/tests/test_mathtext.py,sha256=2S1i3BLNffEqz1t-mehhWBROBcsdBZsdgrticxW6Xrw,24549
matplotlib/tests/test_matplotlib.py,sha256=ZQgD9x7UVWFnVDOm9K5iKfobz6BtMtFkgyVRQ5QezYY,3368
matplotlib/tests/test_mlab.py,sha256=d4qMyogTFMrvlRZEpDs7SjhSmmCnBUMNgSX2bJU6eDk,42269
matplotlib/tests/test_multivariate_colormaps.py,sha256=wGc08cdUTBDJvhUiwVqTrBS7o19Ei-wQRnWlkny7h5M,20785
matplotlib/tests/test_offsetbox.py,sha256=VXFfgputVnLodHhK5ofEWqge42trRSvIm2areAbv1ZQ,16656
matplotlib/tests/test_patches.py,sha256=FeQGNDS0G8DJNWCAQVit2vN4JPN0ARNougGOTCNa3Fk,33592
matplotlib/tests/test_path.py,sha256=R3oTrr6kS1Nu_q8zeK1uxrrbnQI8NMb5B7yTk8R1eRs,23254
matplotlib/tests/test_patheffects.py,sha256=q7JA5VbZU5Zsqvc2eGM7YhB9TClkNp3akQCZVsHrNQU,8109
matplotlib/tests/test_pickle.py,sha256=2VcsJ6QwN9vP4cvkuUCZD7uwKyd1rOCDIaYPEBr3u6E,10014
matplotlib/tests/test_png.py,sha256=d6u6UkU71T6ULxDVSdMkIT3CUAL46dEuDuNrnjZe5BE,1407
matplotlib/tests/test_polar.py,sha256=pPXBMmeUIUZhykYyZa5mrrjFL9GJE3vsaTX94cRjstU,18306
matplotlib/tests/test_preprocess_data.py,sha256=cIVICUi1iahMQS30sqI5IlT2RYJRH2gJ0z60FyuYUFk,11363
matplotlib/tests/test_pyplot.py,sha256=5-Rv0g037ifl64vWybmlV8y22YjuY5lVX5aPvnzihiA,13910
matplotlib/tests/test_quiver.py,sha256=5zSF_0DpoDXPBvGWD6iu1VfIUna15npLd2UyCG6_Ksg,11953
matplotlib/tests/test_rcparams.py,sha256=PLos0asOBQMn0hjrVP_itywcbZjT8og_TaB3tqSFHac,26506
matplotlib/tests/test_sankey.py,sha256=yg4i-qHT5MljFzFGViOnHbMkumM8bhPwRgoQ5M6CUEs,3900
matplotlib/tests/test_scale.py,sha256=fqLu88lVy7-JZFNslpgiid11_TIivSkM9DxVnHwZJYo,8429
matplotlib/tests/test_simplification.py,sha256=IJ1yEz2Y58QtKRRHz41NaXVS4kkRipaE_Un0HgM4K2g,21570
matplotlib/tests/test_skew.py,sha256=_Mjwfgce6WxLH3dafI7irv2Cw0ANifytCtQXdyOStCk,6349
matplotlib/tests/test_sphinxext.py,sha256=9hzVcUgMhtfo_6A_c-SvCYWDQY45Oq-lD6Os4PnRg3s,9937
matplotlib/tests/test_spines.py,sha256=g2CeF0S73XoGA-T04NvYFalIyZvcq7CpbTvN-ikVFn0,5274
matplotlib/tests/test_streamplot.py,sha256=mSLuZ7E2eEtcI5nKHBZZ_30OYte5awqsXWFWROJYtCQ,5731
matplotlib/tests/test_style.py,sha256=sd6rMLpPBS1hinR2svI1rXZmnXUnMQk1szxSNBUXtzU,6509
matplotlib/tests/test_subplots.py,sha256=-YVCEob48FyLUmdInE8vQ2E2b1txTbqChFb1tDUu16s,11082
matplotlib/tests/test_table.py,sha256=QsrDRpe864LP3zkQBtTciKub5raYz3i3Vrt_Ve3kdzQ,8659
matplotlib/tests/test_testing.py,sha256=eh-1r4PIXcM7hfSKCNTU899QxRYWhZBg6W7x0TDVUxo,1057
matplotlib/tests/test_texmanager.py,sha256=j_5ZG02ztIO-m0oXzd31a13YFw_G7JR7Ldt3fwf-phI,2647
matplotlib/tests/test_text.py,sha256=3RxKrfJJBWKFGSrEvxeCG5rxcvlkIivir8wCj1ls5-M,38473
matplotlib/tests/test_textpath.py,sha256=WLXvT5OzE6Tew4pr87LH-cgioCzM7srgMNRemiMEC5o,271
matplotlib/tests/test_ticker.py,sha256=Bj3hiEgXMBXzDi0Lmg8k-Qg4ChPZrQVqGU0Qfh7Sv84,74759
matplotlib/tests/test_tightlayout.py,sha256=KU0m9BnpbKV-jBdaI4EbDLE5Cf_BLtF0Y9hcFfQ4zlQ,13978
matplotlib/tests/test_transforms.py,sha256=NfWkPWOKAmY1sq_R1T85ct4CYdFmYUjmjtg2uwOYIIY,48671
matplotlib/tests/test_triangulation.py,sha256=hstVYhH09BoVl8L0n6vHXBabniZLUu2KYl6wqZn2Xac,55289
matplotlib/tests/test_type1font.py,sha256=gZZDFi0buFus_aAgOrmtwKqbC36-88UkvM8Afbcngs4,6369
matplotlib/tests/test_units.py,sha256=0v7JNqjkA8MVnvlOeFoYqQ3sW1KjRY_ijp8BGM8ZW_U,11675
matplotlib/tests/test_usetex.py,sha256=a-Y6NuyROPHDGP2ELsOZNSVwBUoGtAv1xQZfisl9lSE,6405
matplotlib/tests/test_widgets.py,sha256=U2FMCCJyKD-pcLbzGzEBAL91-3Fx80wsYdinF7eCOrg,66435
matplotlib/texmanager.py,sha256=k3Ud7vHbtY0CjXmzHP-ZnEAxg6bHVxo2ALve7_e1rKg,15319
matplotlib/texmanager.pyi,sha256=di3gbC9muXKTo5VCrW5ye-e19A1mrOl8e8lvI3b904A,1116
matplotlib/text.py,sha256=xYSkLZiOpew4G1jkLA5pUjtF7qUhOs3yp8IEFMqE6Gg,70856
matplotlib/text.pyi,sha256=EL_-kiqz88kuPs0hss6IucRrOCsLJ4zaKOYRFV5Wbk8,7019
matplotlib/textpath.py,sha256=QmrZQ2Mtfyao6qWg-wmEGAbTwkuJ54AnAzFf9NzZHg4,13254
matplotlib/textpath.pyi,sha256=rqOeTAeQYgm2b2NpetrEX0gMF8PzzW43xS5mNfUA98M,2529
matplotlib/ticker.py,sha256=YoIJyWVlYk3DIZbBl-w6fzkuVES3j1dN4nyf-SHKVDg,107381
matplotlib/ticker.pyi,sha256=YvnQDHxJT9H-iomDZpvBCoYkfRRYSot2MR0H0pjZM78,10604
matplotlib/transforms.py,sha256=ctnOTqgPP4dgHCmSkUayQWL9KkjzWSzQEZALcc9oEzA,99763
matplotlib/transforms.pyi,sha256=0I70FR4ZWmRoo6w5KjbXRrH0dVDmQyPFbGt_sNTYq10,12102
matplotlib/tri/__init__.py,sha256=asnfefKRpJv7sGbfddCMybnJInVDPwgph7g0mpoh2u4,820
matplotlib/tri/__pycache__/__init__.cpython-313.pyc,,
matplotlib/tri/__pycache__/_triangulation.cpython-313.pyc,,
matplotlib/tri/__pycache__/_tricontour.cpython-313.pyc,,
matplotlib/tri/__pycache__/_trifinder.cpython-313.pyc,,
matplotlib/tri/__pycache__/_triinterpolate.cpython-313.pyc,,
matplotlib/tri/__pycache__/_tripcolor.cpython-313.pyc,,
matplotlib/tri/__pycache__/_triplot.cpython-313.pyc,,
matplotlib/tri/__pycache__/_trirefine.cpython-313.pyc,,
matplotlib/tri/__pycache__/_tritools.cpython-313.pyc,,
matplotlib/tri/_triangulation.py,sha256=Ur2lKMOx4NrZxwyi0hBeBnVzicuKaCke0NkrZneSklM,9784
matplotlib/tri/_triangulation.pyi,sha256=pVw1rvpIcl00p7V7E9GcvJSqQWyoxlZXX_p0_VSxTiY,1017
matplotlib/tri/_tricontour.py,sha256=yxeH8QgBub1nTq9qbO5Iiiz81bcLXDeMcJHUS2Ahmns,10220
matplotlib/tri/_tricontour.pyi,sha256=jnsAmVRX0-FOUw9ptUgci9J4T4JQRloKeH8fh8aAi-o,1155
matplotlib/tri/_trifinder.py,sha256=3gUzJZDIwfdsSJUE8hIKso9e1-UGvynUN9HxaqC1EEc,3522
matplotlib/tri/_trifinder.pyi,sha256=dXcZucacAS3Ch6nrDBPh2e3LYZLfZ7VwqpBUBb-vMPo,405
matplotlib/tri/_triinterpolate.py,sha256=4FtyJSoJpHcFxkSkZHZ1aNengVNWqVKF4l78PgCH8O0,62445
matplotlib/tri/_triinterpolate.pyi,sha256=nR0o0Jm0uPH-6l0ft1WRHqYlq-6o84X6M0aX1UJ9IvE,1044
matplotlib/tri/_tripcolor.py,sha256=Z6urFv-naDM3_rhv_pkgIZI53h88aOo9qWc3MH7GmBA,6705
matplotlib/tri/_tripcolor.pyi,sha256=QsA-A2ohj3r_tAElt2-9pzi47JiU01tNlRPDIptqnh4,1781
matplotlib/tri/_triplot.py,sha256=jlHSz36Z5S18zBKc639PlSqdhfl7jHol8ExlddJuDI4,3102
matplotlib/tri/_triplot.pyi,sha256=9USU-BfitrcdQE8yWOUlBX59QBNoHCWivDon9JbDQ0k,446
matplotlib/tri/_trirefine.py,sha256=NG8bsDhZ5EOxMT-MsEWzJm11ZR3_8CAYHlG53IGi0ps,13178
matplotlib/tri/_trirefine.pyi,sha256=J_PmjbeI6UbLaeecgj1OCvGe_sr9UUsNK9NGBSlQ320,1056
matplotlib/tri/_tritools.py,sha256=wC9KVE6UqkWVHpyW9FU4hQdqRVRVmJlhaBF1EXsaD8U,10575
matplotlib/tri/_tritools.pyi,sha256=XWwwvH2nIAmH8k59aRjnLBVQbTwKvd_FzdsRNASCJMw,402
matplotlib/typing.py,sha256=ZxpQ5jAqiprq3UkqWParWJITgIuOi4ZJ6WEM_q3QOis,2439
matplotlib/units.py,sha256=7O-llc8k3GpdotUs2tWcEGgoUHHX-Y7o0R7f-1Jve3k,6429
matplotlib/widgets.py,sha256=zxn9B00f5TEVGe9cIXnQTeG-sGB8s8kBg4IuFHBfyt0,152667
matplotlib/widgets.pyi,sha256=BpbjCZec7n-uOj_cImu-cB1imVC4oeL8rJUwjxbmN4c,15383
mpl_toolkits/axes_grid1/__init__.py,sha256=wiuUCQo1g20SW5T3mFOmI9dGCJY6aDmglpQw5DfszEU,371
mpl_toolkits/axes_grid1/__pycache__/__init__.cpython-313.pyc,,
mpl_toolkits/axes_grid1/__pycache__/anchored_artists.cpython-313.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_divider.cpython-313.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_grid.cpython-313.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_rgb.cpython-313.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_size.cpython-313.pyc,,
mpl_toolkits/axes_grid1/__pycache__/inset_locator.cpython-313.pyc,,
mpl_toolkits/axes_grid1/__pycache__/mpl_axes.cpython-313.pyc,,
mpl_toolkits/axes_grid1/__pycache__/parasite_axes.cpython-313.pyc,,
mpl_toolkits/axes_grid1/anchored_artists.py,sha256=ZO5bIF_29sWrk-lWocKJ_45OQdluf_gu7nLrCtfKtdY,17161
mpl_toolkits/axes_grid1/axes_divider.py,sha256=JD3jGBAqRHRYb1jablrpq_RFQvYtYfwtIIPJgQpbKPc,21892
mpl_toolkits/axes_grid1/axes_grid.py,sha256=sjEbq6hu7YFimoOYVIcaccEGXsSljNPaTLfJjFHWVtM,22344
mpl_toolkits/axes_grid1/axes_rgb.py,sha256=pabgaWJuLTCPw2FlT6Zfy5d0_95CEvaLeosWRTElR98,5227
mpl_toolkits/axes_grid1/axes_size.py,sha256=UAEQ-t0qeJlQcwbbrY4tK2NuSWheFyUXIkk4BUCauHc,7713
mpl_toolkits/axes_grid1/inset_locator.py,sha256=_2U8ZAj_x7gjKhPk40VSbzE7L6wO2VY-OoOMfd6xOJs,19649
mpl_toolkits/axes_grid1/mpl_axes.py,sha256=vFCttnj9JIgY3Mt2eOi-O_FVvdZ6SW_sBtIBFib6bz4,4251
mpl_toolkits/axes_grid1/parasite_axes.py,sha256=809Uy3bLgXIDIGztefcvS9EoZcu3KfMV1QwLzX8qVT4,9404
mpl_toolkits/axes_grid1/tests/__init__.py,sha256=sKLxL9jEJBX7eh5OumtXSOnTriPrJUkujTHFtnJVFrM,365
mpl_toolkits/axes_grid1/tests/__pycache__/__init__.cpython-313.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/conftest.cpython-313.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/test_axes_grid1.cpython-313.pyc,,
mpl_toolkits/axes_grid1/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/axes_grid1/tests/test_axes_grid1.py,sha256=x-W845Cvl2O-eGGhpxKDayFg7-QQXNA94nODLCFPpJc,29093
mpl_toolkits/axisartist/__init__.py,sha256=RPaNDl22FbmDP7ZRsku1yCqpoNqcclCk0a3rXj3G7fE,631
mpl_toolkits/axisartist/__pycache__/__init__.cpython-313.pyc,,
mpl_toolkits/axisartist/__pycache__/angle_helper.cpython-313.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_divider.cpython-313.pyc,,
mpl_toolkits/axisartist/__pycache__/axis_artist.cpython-313.pyc,,
mpl_toolkits/axisartist/__pycache__/axisline_style.cpython-313.pyc,,
mpl_toolkits/axisartist/__pycache__/axislines.cpython-313.pyc,,
mpl_toolkits/axisartist/__pycache__/floating_axes.cpython-313.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_finder.cpython-313.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_helper_curvelinear.cpython-313.pyc,,
mpl_toolkits/axisartist/__pycache__/parasite_axes.cpython-313.pyc,,
mpl_toolkits/axisartist/angle_helper.py,sha256=-mjKpaR1pLMJuoc0sx0_V3bv0iRPMrpS7r_WI0UYrCc,12952
mpl_toolkits/axisartist/axes_divider.py,sha256=65xSCQ9cHSC3KE7J7HxS4bfsDTAbPmwyz1jJ43BqnBs,122
mpl_toolkits/axisartist/axis_artist.py,sha256=9FY9yXl8eF5QuBvF8-Vipv_rmdpcOlA3Q48a5v7FoeA,38328
mpl_toolkits/axisartist/axisline_style.py,sha256=9jbDkXEzMQiDHR-lDYKZEvTADtJwt2qlN1cErVUUdx0,6723
mpl_toolkits/axisartist/axislines.py,sha256=QxKvChTaRPj0ovvxdrfr3pOzExEIf5svDwxRE7enEXg,16556
mpl_toolkits/axisartist/floating_axes.py,sha256=kfhWKkmiy8tkXoVHvooTidXIlicU861VncElsUcBhLo,10337
mpl_toolkits/axisartist/grid_finder.py,sha256=Hi2zwnQilavgrqWKScuSRba_WBPqqrbmErGU0XyAsOo,12265
mpl_toolkits/axisartist/grid_helper_curvelinear.py,sha256=ofN7pPqEMh3r6bSf9eOHkAZEOrmhiNZV-Yig3jozkC4,12349
mpl_toolkits/axisartist/parasite_axes.py,sha256=Ydi4-0Lbczr6K7Sz1-fRwK4Tm8KlHrOIumx67Xbo_9c,244
mpl_toolkits/axisartist/tests/__init__.py,sha256=sKLxL9jEJBX7eh5OumtXSOnTriPrJUkujTHFtnJVFrM,365
mpl_toolkits/axisartist/tests/__pycache__/__init__.cpython-313.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/conftest.cpython-313.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_angle_helper.cpython-313.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axis_artist.cpython-313.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axislines.cpython-313.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_floating_axes.cpython-313.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_finder.cpython-313.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_helper_curvelinear.cpython-313.pyc,,
mpl_toolkits/axisartist/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/axisartist/tests/test_angle_helper.py,sha256=PwhJwBm2kk4uMyhdO5arQs8IlqSX2vN0hvUzI7YHqrw,5670
mpl_toolkits/axisartist/tests/test_axis_artist.py,sha256=wt3bicVgUPnBX48-dH0Z6hboHgutIgwVpaGkcUZDeVU,2980
mpl_toolkits/axisartist/tests/test_axislines.py,sha256=NXegrvEzVWovshta-qjbUKA2tpQcAbjYbfwkf6tKT6Y,4353
mpl_toolkits/axisartist/tests/test_floating_axes.py,sha256=l24VB1SLrsJZOMMH2jmBny9ETha4AqAM5KokdGOa5Wk,4083
mpl_toolkits/axisartist/tests/test_grid_finder.py,sha256=cwQLDOdcJbAY2E7dr8595yzuNh1_Yh80r_O8WGT2hMY,1156
mpl_toolkits/axisartist/tests/test_grid_helper_curvelinear.py,sha256=OhHej0vCfCjJJknT7yIt4OxZd6OMJCXnFoT3pzqUtTo,7216
mpl_toolkits/mplot3d/__init__.py,sha256=fH9HdMfFMvjbIWqy2gjQnm2m3ae1CvLiuH6LwKHo0kI,49
mpl_toolkits/mplot3d/__pycache__/__init__.cpython-313.pyc,,
mpl_toolkits/mplot3d/__pycache__/art3d.cpython-313.pyc,,
mpl_toolkits/mplot3d/__pycache__/axes3d.cpython-313.pyc,,
mpl_toolkits/mplot3d/__pycache__/axis3d.cpython-313.pyc,,
mpl_toolkits/mplot3d/__pycache__/proj3d.cpython-313.pyc,,
mpl_toolkits/mplot3d/art3d.py,sha256=ovygkCV4P7dQ9xIicczbJyyAbaEy2C1HYnzjuV3COus,50548
mpl_toolkits/mplot3d/axes3d.py,sha256=hCBda2QB6_OYyKEJOG0paPEgDPmLIcrEMkZuKqOUFho,157921
mpl_toolkits/mplot3d/axis3d.py,sha256=eQPWo2TKbRsPY8JUt5drjBbyRdVKBgeT2EF4HGg51oU,29327
mpl_toolkits/mplot3d/proj3d.py,sha256=6Hm6WPzeu_wjfeR8afrQ1nCfjS0p3wjvoSIxJWVlS0s,6349
mpl_toolkits/mplot3d/tests/__init__.py,sha256=sKLxL9jEJBX7eh5OumtXSOnTriPrJUkujTHFtnJVFrM,365
mpl_toolkits/mplot3d/tests/__pycache__/__init__.cpython-313.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/conftest.cpython-313.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_art3d.cpython-313.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_axes3d.cpython-313.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_legend3d.cpython-313.pyc,,
mpl_toolkits/mplot3d/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/mplot3d/tests/test_art3d.py,sha256=AdFcJf0vz1_b-PIn2cVonPHF1vZw2EOHvmjdgBRa5Yo,3317
mpl_toolkits/mplot3d/tests/test_axes3d.py,sha256=Xf5M1DipyVfsBBF8dA3AXg5U0Hts194EfAh57VSupE4,92348
mpl_toolkits/mplot3d/tests/test_legend3d.py,sha256=QGPaoaucJP9KIC68g8zmk4divB_w5PtQc4DMIHMpcA8,4343
pylab.py,sha256=zUXU0l7e7C5jmDSJbM0GLQxBun3xzuXNf1tuoZYA6Xk,110
