#!/usr/bin/env python3
"""
Setup Script for Llama-3 8B Fine-tuning Environment
==================================================

This script helps set up the environment for fine-tuning Llama-3 8B model.
It checks system requirements, installs dependencies, and verifies the setup.

Usage:
    python setup.py
"""

import os
import sys
import subprocess
import platform
import pkg_resources
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def check_cuda():
    """Check CUDA availability"""
    try:
        import torch
        if torch.cuda.is_available():
            cuda_version = torch.version.cuda
            gpu_count = torch.cuda.device_count()
            print(f"✅ CUDA {cuda_version} with {gpu_count} GPU(s)")
            
            # Show GPU details
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            return True
        else:
            print("❌ CUDA not available")
            return False
    except ImportError:
        print("⚠️  PyTorch not installed, cannot check CUDA")
        return False

def check_disk_space():
    """Check available disk space"""
    try:
        import shutil
        free_space = shutil.disk_usage('.').free / 1024**3
        if free_space < 50:
            print(f"⚠️  Low disk space: {free_space:.1f}GB (50GB+ recommended)")
            return False
        print(f"✅ Disk space: {free_space:.1f}GB available")
        return True
    except Exception as e:
        print(f"⚠️  Could not check disk space: {e}")
        return True

def install_requirements():
    """Install required packages"""
    print("\n📦 Installing requirements...")
    
    requirements = [
        "torch>=2.0.0",
        "transformers>=4.36.0",
        "datasets>=2.14.0",
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0",
        "peft>=0.6.0",
        "trl>=0.7.0",
        "scipy",
        "sentencepiece",
        "protobuf",
        "numpy",
        "pandas"
    ]
    
    for requirement in requirements:
        try:
            print(f"Installing {requirement}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", requirement])
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {requirement}: {e}")
            return False
    
    print("✅ All requirements installed successfully!")
    return True

def install_optional_packages():
    """Install optional packages for better performance"""
    print("\n🚀 Installing optional packages for better performance...")
    
    optional_packages = [
        ("flash-attn", "Flash Attention for faster training"),
        ("wandb", "Weights & Biases for experiment tracking"),
        ("tensorboard", "TensorBoard for logging")
    ]
    
    for package, description in optional_packages:
        try:
            print(f"Installing {package} ({description})...")
            if package == "flash-attn":
                subprocess.check_call([sys.executable, "-m", "pip", "install", package, "--no-build-isolation"])
            else:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError:
            print(f"⚠️  Failed to install {package} (optional)")

def verify_installation():
    """Verify that key packages are installed correctly"""
    print("\n🔍 Verifying installation...")
    
    key_packages = [
        "torch",
        "transformers",
        "datasets",
        "accelerate",
        "bitsandbytes"
    ]
    
    for package in key_packages:
        try:
            __import__(package)
            version = pkg_resources.get_distribution(package).version
            print(f"✅ {package} {version}")
        except ImportError:
            print(f"❌ {package} not found")
            return False
        except Exception as e:
            print(f"⚠️  {package}: {e}")
    
    return True

def create_sample_config():
    """Create sample configuration files"""
    print("\n📝 Creating sample configuration...")
    
    # Create sample prompts file for batch inference
    sample_prompts = [
        "What is artificial intelligence?",
        "Explain machine learning in simple terms.",
        "How do neural networks work?",
        "What are the applications of deep learning?",
        "Describe the difference between supervised and unsupervised learning."
    ]
    
    with open("sample_prompts.txt", "w") as f:
        for prompt in sample_prompts:
            f.write(prompt + "\n")
    
    print("✅ Created sample_prompts.txt")

def main():
    """Main setup function"""
    print("🦙 Llama-3 8B Fine-tuning Setup")
    print("=" * 40)
    
    # Check system requirements
    print("\n🔍 Checking system requirements...")
    
    checks = [
        check_python_version(),
        check_disk_space()
    ]
    
    if not all(checks):
        print("\n❌ System requirements not met. Please fix the issues above.")
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("\n❌ Failed to install requirements.")
        sys.exit(1)
    
    # Check CUDA after PyTorch installation
    check_cuda()
    
    # Install optional packages
    install_optional_packages()
    
    # Verify installation
    if not verify_installation():
        print("\n❌ Installation verification failed.")
        sys.exit(1)
    
    # Create sample files
    create_sample_config()
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Review and modify the configuration in llama3_finetune.py")
    print("2. Prepare your training dataset")
    print("3. Run: python llama3_finetune.py")
    print("4. Test your fine-tuned model: python inference_example.py")
    
    print("\n📚 Files created:")
    print("- llama3_finetune.py: Main training script")
    print("- inference_example.py: Inference example")
    print("- requirements.txt: Package requirements")
    print("- config.yaml: Configuration template")
    print("- sample_prompts.txt: Sample prompts for testing")
    print("- README.md: Detailed documentation")

if __name__ == "__main__":
    main()
