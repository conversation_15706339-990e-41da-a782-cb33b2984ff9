#!/usr/bin/env python3
"""
Comprehensive Fine-tuning Script for Meta Llama-3 8B Uncensored Model
=====================================================================

This script provides a complete pipeline for fine-tuning the DevsDoCode/LLama-3-8b-Uncensored
model using Hugging Face Transformers with optimized configurations for memory efficiency
and training performance.

Hardware Requirements:
- Minimum: 24GB VRAM (RTX 3090/4090, A5000, etc.)
- Recommended: 40GB+ VRAM (A100, H100)
- RAM: 32GB+ system RAM recommended
- Storage: 50GB+ free space for model and checkpoints

Author: AI Assistant
Date: 2025-09-25
"""

import os
import sys
import json
import logging
import warnings
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
import gc

import torch
import torch.nn as nn
from torch.utils.data import Dataset
import transformers
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig,
    pipeline
)
from datasets import Dataset as HFDataset, load_dataset
from accelerate import Accelerator
import bitsandbytes as bnb
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('finetune.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
transformers.logging.set_verbosity_error()


@dataclass
class ModelConfig:
    """Configuration class for model parameters - Optimized for RTX 4060 8GB"""
    model_name: str = "microsoft/DialoGPT-medium"  # Smaller 355M model for 8GB VRAM
    max_length: int = 512  # Reduced for memory efficiency
    use_4bit: bool = True  # Enable 4-bit quantization for memory efficiency
    use_nested_quant: bool = True  # Enable nested quantization for better compression
    bnb_4bit_compute_dtype: str = "float16"
    bnb_4bit_quant_type: str = "nf4"
    use_flash_attention: bool = False  # Disable for compatibility


@dataclass
class TrainingConfig:
    """Configuration class for training parameters - AGGRESSIVE optimization for RTX 4060 8GB"""
    output_dir: str = "./dialogpt-finetuned"
    num_train_epochs: int = 5  # More epochs for better learning with higher LoRA rank
    per_device_train_batch_size: int = 8  # Larger batch size - we can handle more with LoRA
    per_device_eval_batch_size: int = 8
    gradient_accumulation_steps: int = 1  # No accumulation needed with larger batch
    learning_rate: float = 1e-4  # Higher learning rate for aggressive LoRA training
    weight_decay: float = 0.01
    warmup_ratio: float = 0.1
    lr_scheduler_type: str = "cosine"  # Cosine for better convergence
    logging_steps: int = 2  # More frequent logging to monitor aggressive training
    save_steps: int = 50  # Very frequent saves
    eval_steps: int = 50
    evaluation_strategy: str = "steps"
    save_total_limit: int = 3  # Keep more checkpoints for aggressive training
    load_best_model_at_end: bool = True
    metric_for_best_model: str = "eval_loss"
    greater_is_better: bool = False
    report_to: str = "none"  # Change to "wandb" if using Weights & Biases
    dataloader_num_workers: int = 0  # Disable multiprocessing for stability
    remove_unused_columns: bool = False
    use_cpu: bool = False


class CustomDataset(Dataset):
    """Custom dataset class for prompt-response pairs"""
    
    def __init__(self, data: List[Dict], tokenizer, max_length: int = 2048):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        # Format the conversation for DialoGPT
        if "prompt" in item and "response" in item:
            text = f"{item['prompt']}<|endoftext|>{item['response']}<|endoftext|>"
        elif "text" in item:
            text = item["text"]
        else:
            raise ValueError("Dataset must contain either 'prompt'+'response' or 'text' fields")
        
        # Tokenize
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding="max_length",
            max_length=self.max_length,
            return_tensors="pt"
        )

        input_ids = encoding["input_ids"].flatten()
        attention_mask = encoding["attention_mask"].flatten()

        return {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "labels": input_ids.clone()
        }


def install_dependencies():
    """Install required dependencies with specific versions"""
    dependencies = [
        "torch>=2.0.0",
        "transformers>=4.36.0",
        "datasets>=2.14.0",
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0",
        "peft>=0.6.0",
        "trl>=0.7.0",
        "scipy",
        "sentencepiece",
        "protobuf"
    ]
    
    logger.info("Installing dependencies...")
    for dep in dependencies:
        os.system(f"pip install {dep}")
    logger.info("Dependencies installed successfully!")


def setup_model_and_tokenizer(config: ModelConfig) -> tuple:
    """
    Load and configure the model and tokenizer with optimizations
    
    Args:
        config: ModelConfig instance with model parameters
        
    Returns:
        tuple: (model, tokenizer)
    """
    logger.info(f"Loading model: {config.model_name}")
    
    # Configure quantization for memory efficiency
    if config.use_4bit:
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_use_double_quant=config.use_nested_quant,
            bnb_4bit_quant_type=config.bnb_4bit_quant_type,
            bnb_4bit_compute_dtype=getattr(torch, config.bnb_4bit_compute_dtype)
        )
    else:
        bnb_config = None
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        config.model_name,
        trust_remote_code=True,
        use_fast=True
    )
    
    # Add padding token if not present
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
        tokenizer.pad_token_id = tokenizer.eos_token_id
    
    # Load model with optimizations
    model = AutoModelForCausalLM.from_pretrained(
        config.model_name,
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16 if config.bnb_4bit_compute_dtype == "float16" else torch.bfloat16,
        trust_remote_code=True,
        use_cache=False  # Disable cache for training
    )
    
    # Prepare model for k-bit training if quantized
    if bnb_config is not None:
        model = prepare_model_for_kbit_training(model)

    # Enable gradient checkpointing for memory efficiency
    model.gradient_checkpointing_enable()

    # Add AGGRESSIVE LoRA adapters for maximum efficiency
    lora_config = LoraConfig(
        r=64,  # MUCH higher rank for more trainable parameters
        lora_alpha=128,  # Higher alpha for stronger adaptation
        target_modules=["c_attn", "c_proj", "c_fc", "lm_head"],  # Target ALL key modules including output
        lora_dropout=0.05,  # Lower dropout for more aggressive training
        bias="lora_only",  # Train bias terms in LoRA layers
        task_type="CAUSAL_LM",
        modules_to_save=["lm_head"],  # Save the language model head
    )

    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()

    # Prepare model for training
    model.train()

    logger.info(f"Model loaded successfully. Total parameters: {model.num_parameters():,}")
    logger.info(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    logger.info(f"Model device: {next(model.parameters()).device}")

    return model, tokenizer


def prepare_dataset(tokenizer, config: ModelConfig) -> tuple:
    """
    Prepare training and validation datasets
    
    Args:
        tokenizer: Loaded tokenizer
        config: ModelConfig instance
        
    Returns:
        tuple: (train_dataset, eval_dataset)
    """
    logger.info("Preparing datasets...")
    
    # Use AGGRESSIVE training dataset for maximum learning
    logger.info("Using AGGRESSIVE training dataset for maximum LoRA efficiency...")

    # Expanded dataset with diverse conversation patterns
    base_data = [
        {"prompt": "Hello", "response": "Hi there! How can I help you today?"},
        {"prompt": "What is AI?", "response": "AI is artificial intelligence, the simulation of human intelligence in machines that can learn, reason, and make decisions."},
        {"prompt": "How are you?", "response": "I'm doing well, thank you for asking! How are you doing today?"},
        {"prompt": "Tell me a joke", "response": "Why don't scientists trust atoms? Because they make up everything! Here's another: Why did the scarecrow win an award? He was outstanding in his field!"},
        {"prompt": "What's the weather like?", "response": "I don't have access to current weather data, but I'd be happy to help with other questions! Is there something specific you'd like to know?"},
        {"prompt": "Explain machine learning", "response": "Machine learning is a subset of AI where computers learn patterns from data without explicit programming. It involves algorithms that improve automatically through experience."},
        {"prompt": "What is Python?", "response": "Python is a popular, high-level programming language known for its simplicity, readability, and versatility. It's widely used in web development, data science, AI, and automation."},
        {"prompt": "How do computers work?", "response": "Computers process information using binary code (0s and 1s) and execute instructions through their processors. They have memory to store data and programs that tell them what to do."},
        {"prompt": "What is the internet?", "response": "The internet is a global network of interconnected computers that share information using standardized protocols. It enables communication, data sharing, and access to vast resources worldwide."},
        {"prompt": "Goodbye", "response": "Goodbye! Have a great day and feel free to come back anytime you have questions!"},
        {"prompt": "Can you help me?", "response": "Absolutely! I'm here to help. What do you need assistance with?"},
        {"prompt": "What's your favorite color?", "response": "I don't have personal preferences like favorite colors, but I find the concept of color perception fascinating! What's your favorite color?"},
        {"prompt": "Tell me about space", "response": "Space is the vast, mostly empty expanse that exists beyond Earth's atmosphere. It contains stars, planets, galaxies, and countless mysteries we're still discovering."},
        {"prompt": "How do I learn programming?", "response": "Start with a beginner-friendly language like Python, practice regularly with small projects, use online resources, and don't be afraid to make mistakes - they're part of learning!"},
        {"prompt": "What is love?", "response": "Love is a complex emotion involving deep affection, care, and connection between people. It can take many forms - romantic, familial, friendship, and self-love."},
    ]

    # Multiply by 20 for aggressive training (300 samples total)
    train_data = base_data * 20
    
    # Split into train/eval (80/20)
    split_idx = int(0.8 * len(train_data))
    train_split = train_data[:split_idx]
    eval_split = train_data[split_idx:]
    
    # Create datasets
    train_dataset = CustomDataset(train_split, tokenizer, config.max_length)
    eval_dataset = CustomDataset(eval_split, tokenizer, config.max_length) if eval_split else None
    
    logger.info(f"Training samples: {len(train_dataset)}")
    logger.info(f"Evaluation samples: {len(eval_dataset) if eval_dataset else 0}")
    
    return train_dataset, eval_dataset


def setup_training_arguments(config: TrainingConfig) -> TrainingArguments:
    """
    Configure training arguments with optimized settings
    
    Args:
        config: TrainingConfig instance
        
    Returns:
        TrainingArguments: Configured training arguments
    """
    return TrainingArguments(
        output_dir=config.output_dir,
        num_train_epochs=config.num_train_epochs,
        per_device_train_batch_size=config.per_device_train_batch_size,
        per_device_eval_batch_size=config.per_device_eval_batch_size,
        gradient_accumulation_steps=config.gradient_accumulation_steps,
        learning_rate=config.learning_rate,
        weight_decay=config.weight_decay,
        warmup_ratio=config.warmup_ratio,
        lr_scheduler_type=config.lr_scheduler_type,
        logging_steps=config.logging_steps,
        save_steps=config.save_steps,
        eval_steps=config.eval_steps,
        eval_strategy=config.evaluation_strategy,
        save_total_limit=config.save_total_limit,
        load_best_model_at_end=config.load_best_model_at_end,
        metric_for_best_model=config.metric_for_best_model,
        greater_is_better=config.greater_is_better,
        report_to=config.report_to,
        dataloader_num_workers=config.dataloader_num_workers,
        remove_unused_columns=config.remove_unused_columns,
        fp16=True,  # Enable mixed precision training
        gradient_checkpointing=True,
        dataloader_pin_memory=False,
        group_by_length=True,  # Group sequences by length for efficiency
        ddp_find_unused_parameters=False,
    )


def create_trainer(model, tokenizer, train_dataset, eval_dataset, training_args) -> Trainer:
    """
    Create and configure the Trainer instance

    Args:
        model: Loaded model
        tokenizer: Loaded tokenizer
        train_dataset: Training dataset
        eval_dataset: Evaluation dataset (optional)
        training_args: Training arguments

    Returns:
        Trainer: Configured trainer instance
    """
    # Data collator for language modeling
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,  # We're doing causal LM, not masked LM
        pad_to_multiple_of=None,  # Disable padding to multiple
    )

    # Create trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        data_collator=data_collator,
        tokenizer=tokenizer,
    )

    return trainer


def monitor_gpu_memory():
    """Monitor and log GPU memory usage"""
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            memory_allocated = torch.cuda.memory_allocated(i) / 1024**3  # GB
            memory_reserved = torch.cuda.memory_reserved(i) / 1024**3   # GB
            logger.info(f"GPU {i} - Allocated: {memory_allocated:.2f}GB, Reserved: {memory_reserved:.2f}GB")


def test_model_inference(model, tokenizer, test_prompt: str = "What is artificial intelligence?"):
    """
    Test the fine-tuned model with a simple inference

    Args:
        model: Fine-tuned model
        tokenizer: Tokenizer
        test_prompt: Test prompt for inference
    """
    logger.info("Testing model inference...")

    # Format prompt for DialoGPT
    formatted_prompt = f"{test_prompt}<|endoftext|>"

    # Tokenize
    inputs = tokenizer(formatted_prompt, return_tensors="pt").to(model.device)

    # Generate
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=100,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            pad_token_id=tokenizer.eos_token_id
        )

    # Decode response
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    logger.info(f"Test prompt: {test_prompt}")
    logger.info(f"Model response: {response[len(formatted_prompt):]}")


def save_model_and_tokenizer(model, tokenizer, output_dir: str):
    """
    Save the fine-tuned model and tokenizer

    Args:
        model: Fine-tuned model
        tokenizer: Tokenizer
        output_dir: Output directory
    """
    logger.info(f"Saving model and tokenizer to {output_dir}")

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Save model and tokenizer
    model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)

    # Save training configuration
    config_path = os.path.join(output_dir, "training_config.json")
    with open(config_path, "w") as f:
        json.dump({
            "model_name": "DevsDoCode/LLama-3-8b-Uncensored",
            "fine_tuned": True,
            "training_date": "2025-09-25",
            "notes": "Fine-tuned using custom script with optimized configurations"
        }, f, indent=2)

    logger.info("Model and tokenizer saved successfully!")


def load_finetuned_model(model_path: str):
    """
    Example function to load the fine-tuned model later

    Args:
        model_path: Path to the fine-tuned model

    Returns:
        tuple: (model, tokenizer)
    """
    logger.info(f"Loading fine-tuned model from {model_path}")

    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True
    )

    return model, tokenizer


def cleanup_memory():
    """Clean up GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()


def main():
    """Main training pipeline"""
    logger.info("Starting DialoGPT fine-tuning pipeline (optimized for RTX 4060)...")

    # Check GPU availability
    if not torch.cuda.is_available():
        logger.error("CUDA is not available. This script requires GPU for training.")
        sys.exit(1)

    logger.info(f"Using {torch.cuda.device_count()} GPU(s)")
    logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
    logger.info(f"VRAM: {torch.cuda.get_device_properties(0).total_memory/1024**3:.1f}GB")
    monitor_gpu_memory()

    try:
        # Initialize configurations
        model_config = ModelConfig()
        training_config = TrainingConfig()

        # Load model and tokenizer
        model, tokenizer = setup_model_and_tokenizer(model_config)

        # Prepare datasets
        train_dataset, eval_dataset = prepare_dataset(tokenizer, model_config)

        # Setup training arguments
        training_args = setup_training_arguments(training_config)

        # Create trainer
        trainer = create_trainer(model, tokenizer, train_dataset, eval_dataset, training_args)

        # Monitor memory before training
        monitor_gpu_memory()

        # Start training
        logger.info("Starting training...")
        trainer.train()

        # Save the model
        save_model_and_tokenizer(model, tokenizer, training_config.output_dir)

        # Test inference
        test_model_inference(model, tokenizer)

        logger.info("Training completed successfully!")

    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise
    finally:
        cleanup_memory()


if __name__ == "__main__":
    # Uncomment the line below to install dependencies
    # install_dependencies()

    main()
