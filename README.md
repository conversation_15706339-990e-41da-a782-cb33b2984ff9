# Llama-3 8B Fine-tuning Script

A comprehensive Python script for fine-tuning the Meta Llama-3 8B uncensored model using Hugging Face Transformers with memory-optimized configurations.

## 🚀 Features

- **Memory Optimized**: 4-bit quantization, gradient checkpointing, and optimized batch sizes
- **Production Ready**: Comprehensive error handling, logging, and monitoring
- **Flexible Configuration**: Easy-to-modify configuration files
- **Hardware Adaptive**: Configurations for different GPU setups
- **Complete Pipeline**: From data loading to model saving and testing

## 📋 Requirements

### Hardware Requirements

| GPU | VRAM | Batch Size | Quantization | Status |
|-----|------|------------|--------------|---------|
| RTX 3090/4090 | 24GB | 1 | 4-bit | ✅ Supported |
| RTX A5000 | 24GB | 1 | 4-bit | ✅ Supported |
| A100 40GB | 40GB | 2 | Optional | ✅ Recommended |
| A100 80GB | 80GB | 4 | No | ✅ Optimal |

### Software Requirements

- Python 3.8+
- CUDA 11.8+ or 12.0+
- 32GB+ System RAM (recommended)
- 50GB+ Free disk space

## 🛠️ Installation

1. **Clone or download the script files**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Optional: Install Flash Attention (for better performance)**
   ```bash
   pip install flash-attn --no-build-isolation
   ```

## 🚀 Quick Start

### Basic Usage

```python
# Run with default settings
python llama3_finetune.py
```

### Custom Dataset Format

Create your dataset in this format:

```python
train_data = [
    {
        "prompt": "Your question or instruction here",
        "response": "The expected response here"
    },
    # Add more examples...
]
```

### Configuration

Modify the `ModelConfig` and `TrainingConfig` classes in the script or use the provided `config.yaml`:

```python
# Example: Adjust for your hardware
model_config = ModelConfig(
    use_4bit=True,  # Set to False if you have >40GB VRAM
    max_length=2048
)

training_config = TrainingConfig(
    per_device_train_batch_size=2,  # Increase if you have more VRAM
    gradient_accumulation_steps=4,
    num_train_epochs=3
)
```

## 📊 Memory Usage Guide

### RTX 4090 (24GB) Configuration
```python
ModelConfig(
    use_4bit=True,
    max_length=2048
)

TrainingConfig(
    per_device_train_batch_size=1,
    gradient_accumulation_steps=8
)
```

### A100 40GB Configuration
```python
ModelConfig(
    use_4bit=False,
    max_length=2048
)

TrainingConfig(
    per_device_train_batch_size=2,
    gradient_accumulation_steps=4
)
```

## 📁 Output Structure

After training, you'll find:

```
./llama3-finetuned/
├── config.json              # Model configuration
├── generation_config.json   # Generation parameters
├── pytorch_model.bin        # Model weights
├── tokenizer.json          # Tokenizer
├── tokenizer_config.json   # Tokenizer config
├── special_tokens_map.json # Special tokens
└── training_config.json    # Training metadata
```

## 🔧 Advanced Usage

### Using Custom Datasets

```python
# Load your custom dataset
def load_custom_dataset():
    # Your data loading logic here
    return train_data, eval_data

# Modify the prepare_dataset function
train_dataset, eval_dataset = load_custom_dataset()
```

### Monitoring Training

1. **Enable Weights & Biases logging:**
   ```python
   training_config.report_to = "wandb"
   ```

2. **Monitor GPU memory:**
   ```python
   monitor_gpu_memory()  # Called automatically in the script
   ```

### Loading Fine-tuned Model

```python
from transformers import AutoTokenizer, AutoModelForCausalLM

# Load your fine-tuned model
model_path = "./llama3-finetuned"
tokenizer = AutoTokenizer.from_pretrained(model_path)
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    device_map="auto",
    torch_dtype=torch.float16
)

# Generate text
prompt = "What is artificial intelligence?"
inputs = tokenizer(prompt, return_tensors="pt")
outputs = model.generate(**inputs, max_new_tokens=100)
response = tokenizer.decode(outputs[0], skip_special_tokens=True)
```

## 🐛 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce `per_device_train_batch_size` to 1
   - Enable 4-bit quantization
   - Reduce `max_length`

2. **Slow Training**
   - Install Flash Attention
   - Increase `dataloader_num_workers`
   - Use `group_by_length=True`

3. **Model Not Loading**
   - Check internet connection
   - Verify Hugging Face token if needed
   - Ensure sufficient disk space

### Performance Tips

- Use `bf16` instead of `fp16` on newer GPUs (A100, H100)
- Enable gradient checkpointing for memory efficiency
- Use DeepSpeed for multi-GPU training
- Monitor GPU utilization with `nvidia-smi`

## 📝 License

This script is provided as-is for educational and research purposes. Please ensure you comply with the original model's license terms.

## 🤝 Contributing

Feel free to submit issues, feature requests, or improvements to this fine-tuning script.

## 📚 Additional Resources

- [Hugging Face Transformers Documentation](https://huggingface.co/docs/transformers)
- [Llama-3 Model Card](https://huggingface.co/meta-llama/Meta-Llama-3-8B)
- [Fine-tuning Best Practices](https://huggingface.co/docs/transformers/training)
