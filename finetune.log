2025-09-25 17:33:49,300 - INFO - Starting Llama-3 8B fine-tuning pipeline...
2025-09-25 17:33:49,300 - INFO - Using 1 GPU(s)
2025-09-25 17:33:49,300 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 17:33:49,300 - INFO - Loading model: DevsDoCode/LLama-3-8b-Uncensored
2025-09-25 17:33:51,183 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-09-25 17:33:51,190 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-09-25 17:33:51,192 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-09-25 17:33:51,210 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-09-25 17:41:12,760 - INFO - Starting DialoGPT fine-tuning pipeline (optimized for RTX 4060)...
2025-09-25 17:41:12,760 - INFO - Using 1 GPU(s)
2025-09-25 17:41:12,760 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 17:41:12,760 - INFO - VRAM: 8.0GB
2025-09-25 17:41:12,761 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 17:41:12,761 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 17:41:14,012 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-09-25 17:43:33,657 - INFO - Starting DialoGPT fine-tuning pipeline (optimized for RTX 4060)...
2025-09-25 17:43:33,657 - INFO - Using 1 GPU(s)
2025-09-25 17:43:33,657 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 17:43:33,658 - INFO - VRAM: 8.0GB
2025-09-25 17:43:33,658 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 17:43:33,658 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 17:43:53,765 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 17:43:57,559 - INFO - Model loaded successfully. Parameters: 354,823,168
2025-09-25 17:43:57,560 - INFO - Model device: cuda:0
2025-09-25 17:43:57,560 - INFO - Preparing datasets...
2025-09-25 17:43:57,560 - INFO - Using optimized example dataset for RTX 4060...
2025-09-25 17:43:57,560 - INFO - Training samples: 80
2025-09-25 17:43:57,560 - INFO - Evaluation samples: 20
2025-09-25 17:43:57,560 - ERROR - Training failed with error: TrainingArguments.__init__() got an unexpected keyword argument 'evaluation_strategy'
2025-09-25 17:44:44,811 - INFO - Starting DialoGPT fine-tuning pipeline (optimized for RTX 4060)...
2025-09-25 17:44:44,811 - INFO - Using 1 GPU(s)
2025-09-25 17:44:44,811 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 17:44:44,812 - INFO - VRAM: 8.0GB
2025-09-25 17:44:44,812 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 17:44:44,812 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 17:44:45,521 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 17:44:47,495 - INFO - Model loaded successfully. Parameters: 354,823,168
2025-09-25 17:44:47,495 - INFO - Model device: cuda:0
2025-09-25 17:44:47,495 - INFO - Preparing datasets...
2025-09-25 17:44:47,495 - INFO - Using optimized example dataset for RTX 4060...
2025-09-25 17:44:47,496 - INFO - Training samples: 80
2025-09-25 17:44:47,496 - INFO - Evaluation samples: 20
2025-09-25 17:44:47,632 - ERROR - Training failed with error: You cannot perform fine-tuning on purely quantized models. Please attach trainable adapters on top of the quantized model to correctly perform fine-tuning. Please see: https://huggingface.co/docs/transformers/peft for more details
2025-09-25 17:45:31,968 - INFO - Starting DialoGPT fine-tuning pipeline (optimized for RTX 4060)...
2025-09-25 17:45:31,968 - INFO - Using 1 GPU(s)
2025-09-25 17:45:31,968 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 17:45:31,968 - INFO - VRAM: 8.0GB
2025-09-25 17:45:31,968 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 17:45:31,968 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 17:45:32,659 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 17:45:34,561 - INFO - Model loaded successfully. Total parameters: 361,114,624
2025-09-25 17:45:34,563 - INFO - Trainable parameters: 6,291,456
2025-09-25 17:45:34,563 - INFO - Model device: cuda:0
2025-09-25 17:45:34,563 - INFO - Preparing datasets...
2025-09-25 17:45:34,563 - INFO - Using optimized example dataset for RTX 4060...
2025-09-25 17:45:34,564 - INFO - Training samples: 80
2025-09-25 17:45:34,564 - INFO - Evaluation samples: 20
2025-09-25 17:45:34,688 - INFO - GPU 0 - Allocated: 0.40GB, Reserved: 0.69GB
2025-09-25 17:45:34,688 - INFO - Starting training...
2025-09-25 17:45:50,924 - ERROR - Training failed with error: Caught ValueError in DataLoader worker process 0.
Original Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\transformers\tokenization_utils_base.py", line 767, in convert_to_tensors
    tensor = as_tensor(value)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\transformers\tokenization_utils_base.py", line 729, in as_tensor
    return torch.tensor(value)
           ~~~~~~~~~~~~^^^^^^^
ValueError: expected sequence of length 29 at dim 1 (got 23)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\torch\utils\data\_utils\worker.py", line 349, in _worker_loop
    data = fetcher.fetch(index)  # type: ignore[possibly-undefined]
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\torch\utils\data\_utils\fetch.py", line 55, in fetch
    return self.collate_fn(data)
           ~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\transformers\data\data_collator.py", line 46, in __call__
    return self.torch_call(features)
           ~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\transformers\data\data_collator.py", line 1014, in torch_call
    batch = pad_without_fast_tokenizer_warning(
        self.tokenizer, examples, return_tensors="pt", pad_to_multiple_of=self.pad_to_multiple_of
    )
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\transformers\data\data_collator.py", line 67, in pad_without_fast_tokenizer_warning
    padded = tokenizer.pad(*pad_args, **pad_kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\transformers\tokenization_utils_base.py", line 3430, in pad
    return BatchEncoding(batch_outputs, tensor_type=return_tensors)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\transformers\tokenization_utils_base.py", line 240, in __init__
    self.convert_to_tensors(tensor_type=tensor_type, prepend_batch_axis=prepend_batch_axis)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\transformers\tokenization_utils_base.py", line 783, in convert_to_tensors
    raise ValueError(
    ...<4 lines>...
    ) from e
ValueError: Unable to create tensor, you should probably activate truncation and/or padding with 'padding=True' 'truncation=True' to have batched tensors with the same length. Perhaps your features (`labels` in this case) have excessive nesting (inputs type `list` where type `int` is expected).

2025-09-25 17:47:46,805 - INFO - Starting DialoGPT fine-tuning pipeline (optimized for RTX 4060)...
2025-09-25 17:47:46,805 - INFO - Using 1 GPU(s)
2025-09-25 17:47:46,805 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 17:47:46,805 - INFO - VRAM: 8.0GB
2025-09-25 17:47:46,806 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 17:47:46,806 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 17:47:47,480 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 17:47:49,299 - INFO - Model loaded successfully. Total parameters: 361,114,624
2025-09-25 17:47:49,300 - INFO - Trainable parameters: 6,291,456
2025-09-25 17:47:49,300 - INFO - Model device: cuda:0
2025-09-25 17:47:49,300 - INFO - Preparing datasets...
2025-09-25 17:47:49,301 - INFO - Using optimized example dataset for RTX 4060...
2025-09-25 17:47:49,301 - INFO - Training samples: 80
2025-09-25 17:47:49,301 - INFO - Evaluation samples: 20
2025-09-25 17:47:49,432 - INFO - GPU 0 - Allocated: 0.40GB, Reserved: 0.69GB
2025-09-25 17:47:49,432 - INFO - Starting training...
2025-09-25 18:14:27,635 - INFO - Starting DialoGPT fine-tuning pipeline (optimized for RTX 4060)...
2025-09-25 18:14:27,636 - INFO - Using 1 GPU(s)
2025-09-25 18:14:27,636 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 18:14:27,637 - INFO - VRAM: 8.0GB
2025-09-25 18:14:27,637 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 18:14:27,637 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 18:14:28,506 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 18:14:31,259 - INFO - Model loaded successfully. Total parameters: 431,452,160
2025-09-25 18:14:31,261 - INFO - Trainable parameters: 76,850,176
2025-09-25 18:14:31,261 - INFO - Model device: cuda:0
2025-09-25 18:14:31,261 - INFO - Preparing datasets...
2025-09-25 18:14:31,262 - INFO - Using AGGRESSIVE training dataset for maximum LoRA efficiency...
2025-09-25 18:14:31,262 - INFO - Training samples: 240
2025-09-25 18:14:31,262 - INFO - Evaluation samples: 60
2025-09-25 18:14:31,413 - INFO - GPU 0 - Allocated: 0.66GB, Reserved: 0.95GB
2025-09-25 18:14:31,413 - INFO - Starting training...
2025-09-25 18:17:10,483 - ERROR - Training failed with error: Attempting to unscale FP16 gradients.
2025-09-25 18:17:56,161 - INFO - Starting DialoGPT fine-tuning pipeline (optimized for RTX 4060)...
2025-09-25 18:17:56,161 - INFO - Using 1 GPU(s)
2025-09-25 18:17:56,162 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 18:17:56,162 - INFO - VRAM: 8.0GB
2025-09-25 18:17:56,162 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 18:17:56,162 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 18:17:56,995 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 18:18:00,055 - INFO - Model loaded successfully. Total parameters: 431,452,160
2025-09-25 18:18:00,058 - INFO - Trainable parameters: 76,850,176
2025-09-25 18:18:00,058 - INFO - Model device: cuda:0
2025-09-25 18:18:00,058 - INFO - Preparing datasets...
2025-09-25 18:18:00,058 - INFO - Using AGGRESSIVE training dataset for maximum LoRA efficiency...
2025-09-25 18:18:00,058 - INFO - Training samples: 240
2025-09-25 18:18:00,058 - INFO - Evaluation samples: 60
2025-09-25 18:18:00,197 - INFO - GPU 0 - Allocated: 0.66GB, Reserved: 0.95GB
2025-09-25 18:18:00,197 - INFO - Starting training...
2025-09-25 18:32:35,162 - INFO - Starting CODING EXPERT AI fine-tuning pipeline (optimized for RTX 4060)...
2025-09-25 18:32:35,163 - INFO - Using 1 GPU(s)
2025-09-25 18:32:35,163 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 18:32:35,163 - INFO - VRAM: 8.0GB
2025-09-25 18:32:35,164 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 18:32:35,164 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 18:32:36,404 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 18:32:39,995 - INFO - Model loaded successfully. Total parameters: 431,452,160
2025-09-25 18:32:39,998 - INFO - Trainable parameters: 76,850,176
2025-09-25 18:32:39,999 - INFO - Model device: cuda:0
2025-09-25 18:32:39,999 - INFO - Preparing datasets...
2025-09-25 18:32:39,999 - INFO - Using CODING EXPERT dataset for programming and software development mastery...
2025-09-25 18:32:39,999 - INFO - Training samples: 208
2025-09-25 18:32:39,999 - INFO - Evaluation samples: 52
2025-09-25 18:32:40,154 - INFO - GPU 0 - Allocated: 0.66GB, Reserved: 0.95GB
2025-09-25 18:32:40,154 - INFO - Starting training...
2025-09-25 18:44:00,376 - INFO - Target: 70-80% of ChatGPT capabilities with NO restrictions
2025-09-25 18:44:00,376 - INFO - Hardware: RTX 4060 8GB pushed to absolute limits
2025-09-25 18:44:00,376 - INFO - Using 1 GPU(s)
2025-09-25 18:44:00,376 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 18:44:00,377 - INFO - VRAM: 8.0GB
2025-09-25 18:44:00,377 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 18:44:00,377 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 18:44:01,431 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 18:44:43,813 - INFO - Target: 70-80% of ChatGPT capabilities with NO restrictions
2025-09-25 18:44:43,814 - INFO - Hardware: RTX 4060 8GB pushed to absolute limits
2025-09-25 18:44:43,814 - INFO - Using 1 GPU(s)
2025-09-25 18:44:43,814 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 18:44:43,814 - INFO - VRAM: 8.0GB
2025-09-25 18:44:43,815 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 18:44:43,816 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 18:44:45,500 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 18:44:49,347 - INFO - Model loaded successfully. Total parameters: 508,343,296
2025-09-25 18:44:49,354 - INFO - Trainable parameters: 153,791,488
2025-09-25 18:44:49,357 - INFO - Model device: cuda:0
2025-09-25 18:44:49,362 - INFO - Preparing datasets...
2025-09-25 18:44:49,362 - INFO - Loading MAXIMUM PERFORMANCE dataset - Unrestricted AI with complete human knowledge...
2025-09-25 18:44:49,363 - INFO - This will create an AI with 70-80% of ChatGPT's capabilities...
2025-09-25 18:44:49,369 - INFO - MAXIMUM DATASET LOADED: 1450 total samples
2025-09-25 18:44:49,374 - INFO - Domains covered: Programming, Science, Medicine, Business, History, Philosophy, Arts, Life Skills
2025-09-25 18:44:49,379 - INFO - Training samples: 1160
2025-09-25 18:44:49,379 - INFO - Evaluation samples: 290
2025-09-25 18:44:49,630 - INFO - GPU 0 - Allocated: 0.95GB, Reserved: 1.11GB
2025-09-25 18:44:49,631 - INFO - Starting training...
2025-09-25 18:44:52,736 - INFO - Model loaded successfully. Total parameters: 508,343,296
2025-09-25 18:44:52,747 - INFO - Trainable parameters: 153,791,488
2025-09-25 18:44:52,748 - INFO - Model device: cuda:0
2025-09-25 18:44:52,748 - INFO - Preparing datasets...
2025-09-25 18:44:52,749 - INFO - Loading MAXIMUM PERFORMANCE dataset - Unrestricted AI with complete human knowledge...
2025-09-25 18:44:52,750 - INFO - This will create an AI with 70-80% of ChatGPT's capabilities...
2025-09-25 18:44:52,751 - INFO - MAXIMUM DATASET LOADED: 1450 total samples
2025-09-25 18:44:52,751 - INFO - Domains covered: Programming, Science, Medicine, Business, History, Philosophy, Arts, Life Skills
2025-09-25 18:44:52,752 - INFO - Training samples: 1160
2025-09-25 18:44:52,752 - INFO - Evaluation samples: 290
2025-09-25 18:44:52,983 - INFO - GPU 0 - Allocated: 0.95GB, Reserved: 1.11GB
2025-09-25 18:44:52,983 - INFO - Starting training...
2025-09-25 18:47:02,075 - INFO - MAXIMUM PERFORMANCE AI - Unrestricted Comprehensive Training
2025-09-25 18:47:02,075 - INFO - Target: 70-80% of ChatGPT capabilities with NO restrictions
2025-09-25 18:47:02,075 - INFO - Hardware: RTX 4060 8GB pushed to absolute limits
2025-09-25 18:47:02,075 - INFO - Using 1 GPU(s)
2025-09-25 18:47:02,076 - INFO - GPU: NVIDIA GeForce RTX 4060
2025-09-25 18:47:02,076 - INFO - VRAM: 8.0GB
2025-09-25 18:47:02,076 - INFO - GPU 0 - Allocated: 0.00GB, Reserved: 0.00GB
2025-09-25 18:47:02,076 - INFO - Loading model: microsoft/DialoGPT-medium
2025-09-25 18:47:02,813 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
2025-09-25 18:47:05,084 - INFO - Model loaded successfully. Total parameters: 508,343,296
2025-09-25 18:47:05,086 - INFO - Trainable parameters: 153,791,488
2025-09-25 18:47:05,086 - INFO - Model device: cuda:0
2025-09-25 18:47:05,086 - INFO - Preparing datasets...
2025-09-25 18:47:05,086 - INFO - Loading MAXIMUM PERFORMANCE dataset - Unrestricted AI with complete human knowledge...
2025-09-25 18:47:05,087 - INFO - This will create an AI with 70-80% of ChatGPT's capabilities...
2025-09-25 18:47:05,087 - INFO - MAXIMUM DATASET LOADED: 1450 total samples
2025-09-25 18:47:05,087 - INFO - Domains covered: Programming, Science, Medicine, Business, History, Philosophy, Arts, Life Skills
2025-09-25 18:47:05,087 - INFO - Training samples: 1160
2025-09-25 18:47:05,087 - INFO - Evaluation samples: 290
2025-09-25 18:47:05,138 - INFO - GPU 0 - Allocated: 0.95GB, Reserved: 1.11GB
2025-09-25 18:47:05,138 - INFO - Starting training...
