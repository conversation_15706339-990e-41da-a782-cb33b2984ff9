{"best_global_step": 25, "best_metric": 3.902303457260132, "best_model_checkpoint": "./MAXIMUM-AI-UNRESTRICTED\\checkpoint-25", "epoch": 0.3448275862068966, "eval_steps": 25, "global_step": 25, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.013793103448275862, "grad_norm": 14.25157642364502, "learning_rate": 0.0, "loss": 7.3792, "step": 1}, {"epoch": 0.027586206896551724, "grad_norm": 24.139142990112305, "learning_rate": 5.405405405405406e-06, "loss": 7.5235, "step": 2}, {"epoch": 0.041379310344827586, "grad_norm": 20.662925720214844, "learning_rate": 1.0810810810810812e-05, "loss": 7.4775, "step": 3}, {"epoch": 0.05517241379310345, "grad_norm": 39.551700592041016, "learning_rate": 1.6216216216216218e-05, "loss": 7.5931, "step": 4}, {"epoch": 0.06896551724137931, "grad_norm": 17.97701644897461, "learning_rate": 2.1621621621621624e-05, "loss": 7.4307, "step": 5}, {"epoch": 0.08275862068965517, "grad_norm": 13.243189811706543, "learning_rate": 2.702702702702703e-05, "loss": 7.4569, "step": 6}, {"epoch": 0.09655172413793103, "grad_norm": 12.094181060791016, "learning_rate": 3.2432432432432436e-05, "loss": 7.2446, "step": 7}, {"epoch": 0.1103448275862069, "grad_norm": 15.961249351501465, "learning_rate": 3.783783783783784e-05, "loss": 7.5501, "step": 8}, {"epoch": 0.12413793103448276, "grad_norm": 10.808073043823242, "learning_rate": 4.324324324324325e-05, "loss": 7.0885, "step": 9}, {"epoch": 0.13793103448275862, "grad_norm": 21.11432456970215, "learning_rate": 4.8648648648648654e-05, "loss": 6.8905, "step": 10}, {"epoch": 0.15172413793103448, "grad_norm": 12.047744750976562, "learning_rate": 5.405405405405406e-05, "loss": 6.7147, "step": 11}, {"epoch": 0.16551724137931034, "grad_norm": 15.518244743347168, "learning_rate": 5.9459459459459466e-05, "loss": 6.5732, "step": 12}, {"epoch": 0.1793103448275862, "grad_norm": 9.886035919189453, "learning_rate": 6.486486486486487e-05, "loss": 6.3962, "step": 13}, {"epoch": 0.19310344827586207, "grad_norm": 9.462145805358887, "learning_rate": 7.027027027027028e-05, "loss": 6.3101, "step": 14}, {"epoch": 0.20689655172413793, "grad_norm": 9.385098457336426, "learning_rate": 7.567567567567568e-05, "loss": 6.195, "step": 15}, {"epoch": 0.2206896551724138, "grad_norm": 10.02944278717041, "learning_rate": 8.108108108108109e-05, "loss": 5.883, "step": 16}, {"epoch": 0.23448275862068965, "grad_norm": 9.686283111572266, "learning_rate": 8.64864864864865e-05, "loss": 5.3188, "step": 17}, {"epoch": 0.2482758620689655, "grad_norm": 10.759143829345703, "learning_rate": 9.18918918918919e-05, "loss": 5.3375, "step": 18}, {"epoch": 0.2620689655172414, "grad_norm": 11.091740608215332, "learning_rate": 9.729729729729731e-05, "loss": 5.5347, "step": 19}, {"epoch": 0.27586206896551724, "grad_norm": 8.716623306274414, "learning_rate": 0.0001027027027027027, "loss": 5.0768, "step": 20}, {"epoch": 0.2896551724137931, "grad_norm": 11.82148551940918, "learning_rate": 0.00010810810810810812, "loss": 4.8248, "step": 21}, {"epoch": 0.30344827586206896, "grad_norm": 11.177566528320312, "learning_rate": 0.00011351351351351351, "loss": 4.8322, "step": 22}, {"epoch": 0.31724137931034485, "grad_norm": 9.284028053283691, "learning_rate": 0.00011891891891891893, "loss": 4.3292, "step": 23}, {"epoch": 0.3310344827586207, "grad_norm": 9.155341148376465, "learning_rate": 0.00012432432432432433, "loss": 4.2356, "step": 24}, {"epoch": 0.3448275862068966, "grad_norm": 8.34678840637207, "learning_rate": 0.00012972972972972974, "loss": 4.1426, "step": 25}, {"epoch": 0.3448275862068966, "eval_loss": 3.902303457260132, "eval_runtime": 16.4365, "eval_samples_per_second": 17.644, "eval_steps_per_second": 4.441, "step": 25}], "logging_steps": 1, "max_steps": 730, "num_input_tokens_seen": 0, "num_train_epochs": 10, "save_steps": 25, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 496887870259200.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}