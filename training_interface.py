#!/usr/bin/env python3
"""
MAXIMUM PERFORMANCE AI - Training Interface
==========================================
Real-time visual interface for monitoring AI training progress.
Shows GPU usage, training metrics, and progress in real-time.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import time
import subprocess
import psutil
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class TrainingInterface:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("MAXIMUM PERFORMANCE AI - Training Interface")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1e1e1e')
        
        # Training state
        self.training_process = None
        self.is_training = False
        self.training_data = {
            'epochs': [],
            'loss': [],
            'gpu_usage': [],
            'memory_usage': [],
            'timestamps': []
        }
        
        self.setup_interface()
        self.start_monitoring()
    
    def setup_interface(self):
        """Create the visual interface"""
        
        # Title
        title_frame = tk.Frame(self.root, bg='#1e1e1e')
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(title_frame, 
                              text="🚀 MAXIMUM PERFORMANCE AI TRAINING 🚀",
                              font=('Arial', 16, 'bold'),
                              fg='#00ff00', bg='#1e1e1e')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame,
                                 text="Target: 70-80% ChatGPT Capability | RTX 4060 8GB",
                                 font=('Arial', 10),
                                 fg='#ffffff', bg='#1e1e1e')
        subtitle_label.pack()
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left panel - Controls and Status
        left_frame = tk.Frame(main_frame, bg='#2d2d2d', relief='raised', bd=2)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        
        # Control buttons
        control_frame = tk.LabelFrame(left_frame, text="Training Control", 
                                     fg='white', bg='#2d2d2d', font=('Arial', 10, 'bold'))
        control_frame.pack(fill='x', padx=5, pady=5)
        
        self.start_button = tk.Button(control_frame, text="START TRAINING",
                                     command=self.start_training,
                                     bg='#00aa00', fg='white', font=('Arial', 10, 'bold'))
        self.start_button.pack(fill='x', padx=5, pady=2)
        
        self.stop_button = tk.Button(control_frame, text="STOP TRAINING",
                                    command=self.stop_training,
                                    bg='#aa0000', fg='white', font=('Arial', 10, 'bold'),
                                    state='disabled')
        self.stop_button.pack(fill='x', padx=5, pady=2)
        
        # Status display
        status_frame = tk.LabelFrame(left_frame, text="System Status",
                                    fg='white', bg='#2d2d2d', font=('Arial', 10, 'bold'))
        status_frame.pack(fill='x', padx=5, pady=5)
        
        self.status_text = scrolledtext.ScrolledText(status_frame, height=15, width=30,
                                                    bg='#1e1e1e', fg='#00ff00',
                                                    font=('Consolas', 9))
        self.status_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Training metrics
        metrics_frame = tk.LabelFrame(left_frame, text="Training Metrics",
                                     fg='white', bg='#2d2d2d', font=('Arial', 10, 'bold'))
        metrics_frame.pack(fill='x', padx=5, pady=5)
        
        self.metrics_labels = {}
        metrics = ['Epoch', 'Loss', 'GPU Usage', 'Memory', 'ETA']
        for metric in metrics:
            frame = tk.Frame(metrics_frame, bg='#2d2d2d')
            frame.pack(fill='x', padx=5, pady=2)
            
            tk.Label(frame, text=f"{metric}:", fg='white', bg='#2d2d2d',
                    font=('Arial', 9)).pack(side='left')
            
            self.metrics_labels[metric] = tk.Label(frame, text="--", fg='#00ff00', bg='#2d2d2d',
                                                  font=('Arial', 9, 'bold'))
            self.metrics_labels[metric].pack(side='right')
        
        # Right panel - Graphs
        right_frame = tk.Frame(main_frame, bg='#2d2d2d', relief='raised', bd=2)
        right_frame.pack(side='right', fill='both', expand=True)
        
        # Create matplotlib figure
        self.fig, ((self.ax1, self.ax2), (self.ax3, self.ax4)) = plt.subplots(2, 2, figsize=(10, 8))
        self.fig.patch.set_facecolor('#2d2d2d')
        
        for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
            ax.set_facecolor('#1e1e1e')
            ax.tick_params(colors='white')
            ax.spines['bottom'].set_color('white')
            ax.spines['top'].set_color('white')
            ax.spines['right'].set_color('white')
            ax.spines['left'].set_color('white')
        
        self.ax1.set_title('Training Loss', color='white')
        self.ax2.set_title('GPU Usage %', color='white')
        self.ax3.set_title('Memory Usage GB', color='white')
        self.ax4.set_title('Training Progress', color='white')
        
        # Embed matplotlib in tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, right_frame)
        self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=5, pady=5)
        
        # Initial status
        self.log_status("MAXIMUM PERFORMANCE AI Interface Ready")
        self.log_status("Hardware: RTX 4060 8GB")
        self.log_status("Target: 70-80% ChatGPT Capability")
        self.log_status("Click START TRAINING to begin...")
    
    def log_status(self, message):
        """Add message to status log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.status_text.see(tk.END)
        self.root.update()
    
    def start_training(self):
        """Start the training process"""
        if self.is_training:
            return
        
        self.is_training = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        self.log_status("🚀 STARTING MAXIMUM PERFORMANCE TRAINING...")
        self.log_status("Loading comprehensive dataset...")
        self.log_status("Initializing LoRA with 30%+ trainable parameters...")
        
        # Start training in separate thread
        self.training_thread = threading.Thread(target=self.run_training)
        self.training_thread.daemon = True
        self.training_thread.start()
    
    def run_training(self):
        """Run the actual training process"""
        try:
            # Start the training script
            self.training_process = subprocess.Popen(
                ['python', 'llama3_finetune.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Monitor training output
            for line in iter(self.training_process.stdout.readline, ''):
                if line:
                    self.log_status(line.strip())
                    self.parse_training_output(line)
                
                if not self.is_training:
                    break
            
            self.training_process.wait()
            
        except Exception as e:
            self.log_status(f"Training error: {e}")
        finally:
            self.training_finished()
    
    def parse_training_output(self, line):
        """Parse training output for metrics"""
        try:
            # Look for training metrics in the output
            if "loss:" in line.lower():
                # Extract loss value
                parts = line.split("loss:")
                if len(parts) > 1:
                    loss_str = parts[1].split()[0]
                    loss = float(loss_str)
                    self.training_data['loss'].append(loss)
                    self.metrics_labels['Loss'].config(text=f"{loss:.4f}")
            
            if "epoch" in line.lower():
                # Extract epoch
                if "epoch" in line.lower():
                    parts = line.lower().split("epoch")
                    if len(parts) > 1:
                        epoch_str = parts[1].split()[0].replace(":", "")
                        try:
                            epoch = int(float(epoch_str))
                            self.metrics_labels['Epoch'].config(text=str(epoch))
                        except:
                            pass
            
            # Update graphs
            self.update_graphs()
            
        except Exception as e:
            pass  # Ignore parsing errors
    
    def update_graphs(self):
        """Update the training graphs"""
        try:
            # Clear and update loss graph
            if self.training_data['loss']:
                self.ax1.clear()
                self.ax1.plot(self.training_data['loss'], color='#00ff00', linewidth=2)
                self.ax1.set_title('Training Loss', color='white')
                self.ax1.set_facecolor('#1e1e1e')
            
            # Update GPU usage
            gpu_usage = self.get_gpu_usage()
            self.training_data['gpu_usage'].append(gpu_usage)
            self.metrics_labels['GPU Usage'].config(text=f"{gpu_usage}%")
            
            if len(self.training_data['gpu_usage']) > 1:
                self.ax2.clear()
                self.ax2.plot(self.training_data['gpu_usage'][-50:], color='#ff6600', linewidth=2)
                self.ax2.set_title('GPU Usage %', color='white')
                self.ax2.set_facecolor('#1e1e1e')
                self.ax2.set_ylim(0, 100)
            
            # Update memory usage
            memory_usage = self.get_memory_usage()
            self.training_data['memory_usage'].append(memory_usage)
            self.metrics_labels['Memory'].config(text=f"{memory_usage:.1f}GB")
            
            if len(self.training_data['memory_usage']) > 1:
                self.ax3.clear()
                self.ax3.plot(self.training_data['memory_usage'][-50:], color='#6600ff', linewidth=2)
                self.ax3.set_title('Memory Usage GB', color='white')
                self.ax3.set_facecolor('#1e1e1e')
            
            # Refresh canvas
            self.canvas.draw()
            
        except Exception as e:
            pass  # Ignore update errors
    
    def get_gpu_usage(self):
        """Get current GPU usage percentage"""
        try:
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
            return utilization.gpu
        except:
            return 0
    
    def get_memory_usage(self):
        """Get current GPU memory usage in GB"""
        try:
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            return memory_info.used / 1024**3
        except:
            return 0
    
    def stop_training(self):
        """Stop the training process"""
        self.is_training = False
        if self.training_process:
            self.training_process.terminate()
        self.training_finished()
    
    def training_finished(self):
        """Clean up after training"""
        self.is_training = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.log_status("Training finished!")
    
    def start_monitoring(self):
        """Start system monitoring"""
        def monitor():
            while True:
                if self.is_training:
                    self.update_graphs()
                time.sleep(2)
        
        monitor_thread = threading.Thread(target=monitor)
        monitor_thread.daemon = True
        monitor_thread.start()
    
    def run(self):
        """Start the interface"""
        self.root.mainloop()


if __name__ == "__main__":
    # Install required packages
    try:
        import matplotlib
        import pynvml
    except ImportError:
        print("Installing required packages...")
        os.system("pip install matplotlib pynvml")
    
    # Launch interface
    interface = TrainingInterface()
    interface.run()
