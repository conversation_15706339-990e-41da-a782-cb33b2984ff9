{"alpha_pattern": {}, "auto_mapping": null, "base_model_name_or_path": "microsoft/DialoGPT-medium", "bias": "all", "corda_config": null, "eva_config": null, "exclude_modules": null, "fan_in_fan_out": true, "inference_mode": true, "init_lora_weights": true, "layer_replication": null, "layers_pattern": null, "layers_to_transform": null, "loftq_config": {}, "lora_alpha": 256, "lora_bias": false, "lora_dropout": 0.01, "megatron_config": null, "megatron_core": "megatron.core", "modules_to_save": ["lm_head", "wte"], "peft_type": "LORA", "qalora_group_size": 16, "r": 128, "rank_pattern": {}, "revision": null, "target_modules": ["c_attn", "wpe", "c_fc", "wte", "c_proj", "lm_head"], "target_parameters": null, "task_type": "CAUSAL_LM", "trainable_token_indices": null, "use_dora": false, "use_qalora": false, "use_rslora": false}