#!/usr/bin/env python3
"""
ChatGPT-Style Interface for Your Fine-Tuned AI
==============================================
Modern web interface that looks and feels like ChatGPT but uses your custom AI model.
Access via browser at http://localhost:3000
"""

from flask import Flask, render_template_string, request, jsonify, session
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import threading
import time
import uuid
from datetime import datetime
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# Global model state
model_state = {
    'model': None,
    'tokenizer': None,
    'model_loaded': False,
    'model_path': 'microsoft/DialoGPT-medium',  # Use base model for now
    'loading': False
}

# Chat history storage
chat_sessions = {}

CHATGPT_STYLE_HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Custom AI - ChatGPT Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #343541;
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #202123;
            padding: 12px 20px;
            border-bottom: 1px solid #4d4d4f;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }
        
        .model-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10a37f;
        }
        
        .status-dot.loading {
            background: #ff9500;
            animation: pulse 1s infinite;
        }
        
        .status-dot.error {
            background: #ef4444;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .message {
            display: flex;
            gap: 12px;
            max-width: 100%;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .message.user .message-avatar {
            background: #10a37f;
            color: white;
        }
        
        .message.assistant .message-avatar {
            background: #ab68ff;
            color: white;
        }
        
        .message-content {
            background: #444654;
            padding: 12px 16px;
            border-radius: 8px;
            max-width: 70%;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #10a37f;
            margin-left: auto;
        }
        
        .message-content pre {
            background: #2d2d2d;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 8px 0;
            border: 1px solid #555;
        }
        
        .message-content code {
            background: #2d2d2d;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        
        .input-container {
            padding: 20px;
            background: #343541;
            border-top: 1px solid #4d4d4f;
        }
        
        .input-wrapper {
            display: flex;
            gap: 12px;
            align-items: flex-end;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .message-input {
            flex: 1;
            background: #40414f;
            border: 1px solid #565869;
            border-radius: 8px;
            padding: 12px 16px;
            color: #ffffff;
            font-size: 16px;
            font-family: inherit;
            resize: none;
            min-height: 24px;
            max-height: 120px;
            outline: none;
        }
        
        .message-input:focus {
            border-color: #10a37f;
        }
        
        .send-button {
            background: #10a37f;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .send-button:hover:not(:disabled) {
            background: #0d8f6f;
        }
        
        .send-button:disabled {
            background: #565869;
            cursor: not-allowed;
        }
        
        .typing-indicator {
            display: flex;
            gap: 12px;
            align-items: center;
            padding: 12px 0;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10a37f;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }
        
        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #8e8ea0;
        }
        
        .welcome-message h2 {
            font-size: 24px;
            margin-bottom: 12px;
            color: #ffffff;
        }
        
        .welcome-message p {
            font-size: 16px;
            line-height: 1.5;
        }
        
        .model-info {
            background: #444654;
            border-radius: 8px;
            padding: 16px;
            margin: 20px;
            border-left: 4px solid #10a37f;
        }
        
        .model-info h3 {
            color: #10a37f;
            margin-bottom: 8px;
        }
        
        .model-info ul {
            list-style: none;
            padding-left: 0;
        }
        
        .model-info li {
            padding: 4px 0;
            color: #d1d5db;
        }
        
        .model-info li::before {
            content: "✓ ";
            color: #10a37f;
            font-weight: bold;
        }
        
        .clear-chat {
            background: transparent;
            border: 1px solid #565869;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .clear-chat:hover {
            background: #565869;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Your Custom AI - Maximum Performance</h1>
        <div class="model-status">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">Loading model...</span>
            <button class="clear-chat" onclick="clearChat()">Clear Chat</button>
        </div>
    </div>
    
    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="welcome-message">
                <h2>Welcome to Your Custom AI</h2>
                <p>This is your fine-tuned AI with 70-80% of ChatGPT's capabilities and no restrictions!</p>
            </div>
            
            <div class="model-info">
                <h3>Model Capabilities</h3>
                <ul>
                    <li>Programming & Software Development</li>
                    <li>Science & Mathematics</li>
                    <li>Medicine & Health</li>
                    <li>Business & Economics</li>
                    <li>History & Politics</li>
                    <li>Philosophy & Ethics</li>
                    <li>Arts & Literature</li>
                    <li>Practical Life Skills</li>
                    <li>Unrestricted Knowledge Base</li>
                </ul>
            </div>
        </div>
        
        <div class="input-container">
            <div class="input-wrapper">
                <textarea 
                    id="messageInput" 
                    class="message-input" 
                    placeholder="Message your custom AI..."
                    rows="1"
                    onkeydown="handleKeyDown(event)"
                ></textarea>
                <button id="sendButton" class="send-button" onclick="sendMessage()">Send</button>
            </div>
        </div>
    </div>

    <script>
        let isTyping = false;
        let sessionId = generateUUID();
        
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }
        
        function autoResize() {
            const textarea = document.getElementById('messageInput');
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }
        
        document.getElementById('messageInput').addEventListener('input', autoResize);
        
        function addMessage(content, isUser = false) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${isUser ? 'U' : 'AI'}</div>
                <div class="message-content">${formatMessage(content)}</div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function formatMessage(content) {
            // Basic markdown-like formatting
            content = content.replace(/```([\\s\\S]*?)```/g, '<pre><code>$1</code></pre>');
            content = content.replace(/`([^`]+)`/g, '<code>$1</code>');
            content = content.replace(/\\n/g, '<br>');
            return content;
        }
        
        function showTypingIndicator() {
            const messagesContainer = document.getElementById('messages');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message assistant';
            typingDiv.id = 'typingIndicator';
            
            typingDiv.innerHTML = `
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message || isTyping) return;
            
            // Add user message
            addMessage(message, true);
            input.value = '';
            autoResize();
            
            // Disable input
            isTyping = true;
            sendButton.disabled = true;
            showTypingIndicator();
            
            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId
                    })
                });
                
                const data = await response.json();
                
                hideTypingIndicator();
                
                if (data.success) {
                    addMessage(data.response);
                } else {
                    addMessage('Sorry, I encountered an error: ' + data.error);
                }
                
            } catch (error) {
                hideTypingIndicator();
                addMessage('Sorry, I encountered a connection error. Please try again.');
            }
            
            // Re-enable input
            isTyping = false;
            sendButton.disabled = false;
            input.focus();
        }
        
        function clearChat() {
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = `
                <div class="welcome-message">
                    <h2>Chat Cleared</h2>
                    <p>Start a new conversation with your custom AI!</p>
                </div>
            `;
            sessionId = generateUUID();
        }
        
        async function checkModelStatus() {
            try {
                const response = await fetch('/status');
                const data = await response.json();
                
                const statusDot = document.getElementById('statusDot');
                const statusText = document.getElementById('statusText');
                
                if (data.model_loaded) {
                    statusDot.className = 'status-dot';
                    statusText.textContent = 'Model Ready';
                } else if (data.loading) {
                    statusDot.className = 'status-dot loading';
                    statusText.textContent = 'Loading Model...';
                } else {
                    statusDot.className = 'status-dot error';
                    statusText.textContent = 'Model Not Loaded';
                }
            } catch (error) {
                const statusDot = document.getElementById('statusDot');
                const statusText = document.getElementById('statusText');
                statusDot.className = 'status-dot error';
                statusText.textContent = 'Connection Error';
            }
        }
        
        // Check model status every 5 seconds
        setInterval(checkModelStatus, 5000);
        checkModelStatus();
        
        // Focus input on load
        document.getElementById('messageInput').focus();
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(CHATGPT_STYLE_HTML)

@app.route('/status')
def get_status():
    return jsonify(model_state)

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.get_json()
        message = data.get('message', '')
        session_id = data.get('session_id', 'default')
        
        if not model_state['model_loaded']:
            return jsonify({
                'success': False,
                'error': 'Model not loaded. Please wait for the model to load or train a model first.'
            })
        
        # Generate response
        response = generate_response(message)
        
        # Store in chat history
        if session_id not in chat_sessions:
            chat_sessions[session_id] = []
        
        chat_sessions[session_id].append({
            'user': message,
            'assistant': response,
            'timestamp': datetime.now().isoformat()
        })
        
        return jsonify({
            'success': True,
            'response': response
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def load_model():
    """Load the fine-tuned model"""
    global model_state
    
    model_state['loading'] = True
    
    try:
        model_path = model_state['model_path']
        
        # Check if it's a local path or Hugging Face model
        if not os.path.exists(model_path) and '/' in model_path:
            print(f"Loading Hugging Face model: {model_path}")
        elif not os.path.exists(model_path):
            print(f"Model not found at {model_path}")
            print("Please train a model first or check the model path.")
            model_state['loading'] = False
            return
        
        print(f"Loading model from {model_path}...")
        
        # Load tokenizer
        model_state['tokenizer'] = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True
        )
        
        # Load model
        model_state['model'] = AutoModelForCausalLM.from_pretrained(
            model_path,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
        
        model_state['model_loaded'] = True
        model_state['loading'] = False
        
        print("Model loaded successfully!")
        
    except Exception as e:
        print(f"Error loading model: {e}")
        model_state['loading'] = False
        model_state['model_loaded'] = False

def generate_response(prompt):
    """Generate response using the loaded model"""
    try:
        if not model_state['model_loaded']:
            return "Model not loaded. Please wait for the model to load."
        
        # Format prompt for DialoGPT
        formatted_prompt = f"{prompt}<|endoftext|>"
        
        # Tokenize
        inputs = model_state['tokenizer'](
            formatted_prompt,
            return_tensors="pt",
            truncation=True,
            max_length=512
        ).to(model_state['model'].device)
        
        # Generate
        with torch.no_grad():
            outputs = model_state['model'].generate(
                **inputs,
                max_new_tokens=200,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=model_state['tokenizer'].eos_token_id,
                repetition_penalty=1.1
            )
        
        # Decode response
        response = model_state['tokenizer'].decode(
            outputs[0][inputs['input_ids'].shape[1]:],
            skip_special_tokens=True
        )
        
        return response.strip()
        
    except Exception as e:
        return f"Error generating response: {str(e)}"

if __name__ == '__main__':
    print("🚀 ChatGPT-Style Interface for Your Custom AI")
    print("=" * 60)
    print("Starting interface...")
    
    # Load model in background
    model_thread = threading.Thread(target=load_model)
    model_thread.daemon = True
    model_thread.start()
    
    print("Open your browser and go to: http://localhost:3000")
    print("=" * 60)
    
    app.run(host='0.0.0.0', port=3000, debug=False)
