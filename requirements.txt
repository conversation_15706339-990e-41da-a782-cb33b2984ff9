# Core ML libraries
torch>=2.0.0
transformers>=4.36.0
datasets>=2.14.0
accelerate>=0.24.0
bitsandbytes>=0.41.0

# Fine-tuning specific libraries
peft>=0.6.0
trl>=0.7.0

# Utilities
scipy
sentencepiece
protobuf
numpy
pandas

# Optional: Monitoring and logging
wandb  # Uncomment if you want to use Weights & Biases for experiment tracking
tensorboard  # For TensorBoard logging

# Optional: Additional optimizations
flash-attn>=2.0.0  # For flash attention (requires compatible GPU)
deepspeed>=0.9.0   # For advanced distributed training
