# Configuration file for Llama-3 8B Fine-tuning
# Modify these parameters based on your hardware and requirements

model:
  name: "DevsDoCode/LLama-3-8b-Uncensored"
  max_length: 2048
  use_4bit: true  # Enable 4-bit quantization (recommended for <40GB VRAM)
  use_nested_quant: false
  bnb_4bit_compute_dtype: "float16"  # or "bfloat16" for newer GPUs
  bnb_4bit_quant_type: "nf4"
  use_flash_attention: true  # Requires flash-attn package

training:
  output_dir: "./llama3-finetuned"
  num_train_epochs: 3
  per_device_train_batch_size: 1  # Increase if you have more VRAM
  per_device_eval_batch_size: 1
  gradient_accumulation_steps: 8  # Effective batch size = batch_size * gradient_accumulation_steps
  learning_rate: 2e-4
  weight_decay: 0.001
  warmup_ratio: 0.03
  lr_scheduler_type: "cosine"
  logging_steps: 10
  save_steps: 500
  eval_steps: 500
  evaluation_strategy: "steps"  # or "epoch"
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  report_to: "none"  # Change to "wandb" for Weights & Biases logging
  dataloader_num_workers: 4
  remove_unused_columns: false

# Hardware-specific configurations
hardware_configs:
  # For RTX 3090/4090 (24GB VRAM)
  rtx_4090:
    per_device_train_batch_size: 1
    gradient_accumulation_steps: 8
    use_4bit: true
    gradient_checkpointing: true
    
  # For A100 (40GB VRAM)
  a100_40gb:
    per_device_train_batch_size: 2
    gradient_accumulation_steps: 4
    use_4bit: false
    gradient_checkpointing: true
    
  # For A100 (80GB VRAM)
  a100_80gb:
    per_device_train_batch_size: 4
    gradient_accumulation_steps: 2
    use_4bit: false
    gradient_checkpointing: false

# Dataset configuration
dataset:
  # Example datasets you can use:
  # - "tatsu-lab/alpaca"
  # - "yahma/alpaca-cleaned"
  # - "OpenAssistant/oasst1"
  # - Custom dataset path
  name: "tatsu-lab/alpaca"
  subset_size: 1000  # Use null for full dataset
  train_split: 0.8
  validation_split: 0.2
  
# Custom dataset format example
custom_dataset_example:
  - prompt: "What is machine learning?"
    response: "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed."
  - prompt: "Explain neural networks"
    response: "Neural networks are computing systems inspired by biological neural networks. They consist of interconnected nodes (neurons) that process information using a connectionist approach."
