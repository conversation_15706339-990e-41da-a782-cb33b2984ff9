#!/usr/bin/env python3
"""
Inference Example for Fine-tuned Llama-3 8B Model
================================================

This script demonstrates how to load and use your fine-tuned Llama-3 8B model
for text generation and conversation.

Usage:
    python inference_example.py --model_path ./llama3-finetuned
"""

import argparse
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LlamaInference:
    """Class for handling Llama-3 model inference"""
    
    def __init__(self, model_path: str, device: str = "auto"):
        """
        Initialize the inference class
        
        Args:
            model_path: Path to the fine-tuned model
            device: Device to load the model on
        """
        self.model_path = model_path
        self.device = device
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        
        self.load_model()
    
    def load_model(self):
        """Load the fine-tuned model and tokenizer"""
        logger.info(f"Loading model from {self.model_path}")
        
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                device_map=self.device,
                torch_dtype=torch.float16,
                trust_remote_code=True
            )
            
            # Create pipeline for easier inference
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                torch_dtype=torch.float16,
                device_map=self.device
            )
            
            logger.info("Model loaded successfully!")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def generate_response(self, prompt: str, max_new_tokens: int = 200, 
                         temperature: float = 0.7, top_p: float = 0.9) -> str:
        """
        Generate a response to a given prompt
        
        Args:
            prompt: Input prompt
            max_new_tokens: Maximum number of new tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            
        Returns:
            Generated response
        """
        # Format prompt for DialoGPT
        formatted_prompt = f"{prompt}<|endoftext|>"
        
        try:
            # Generate response
            outputs = self.pipeline(
                formatted_prompt,
                max_new_tokens=max_new_tokens,
                temperature=temperature,
                top_p=top_p,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                return_full_text=False
            )
            
            response = outputs[0]['generated_text'].strip()
            return response
            
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            return "Sorry, I encountered an error generating a response."
    
    def chat_mode(self):
        """Interactive chat mode"""
        print("🚀 MAXIMUM PERFORMANCE AI - UNRESTRICTED & COMPREHENSIVE 🚀")
        print("I have 70-80% of ChatGPT's knowledge with NO restrictions!")
        print("Ask me ANYTHING: Programming, Science, Medicine, Business, Philosophy, etc.")
        print("Type 'quit' or 'exit' to end the conversation")
        print("=" * 80)
        
        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if not user_input:
                    continue
                
                print("🧠 AI: ", end="", flush=True)
                response = self.generate_response(user_input)
                print(response)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"Error: {e}")
    
    def batch_inference(self, prompts: list) -> list:
        """
        Run inference on a batch of prompts
        
        Args:
            prompts: List of prompts
            
        Returns:
            List of responses
        """
        responses = []
        
        for i, prompt in enumerate(prompts):
            logger.info(f"Processing prompt {i+1}/{len(prompts)}")
            response = self.generate_response(prompt)
            responses.append(response)
        
        return responses


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Llama-3 Fine-tuned Model Inference")
    parser.add_argument("--model_path", type=str, default="./MAXIMUM-AI-UNRESTRICTED",
                       help="Path to the MAXIMUM PERFORMANCE unrestricted AI model")
    parser.add_argument("--mode", type=str, choices=["chat", "single", "batch"], 
                       default="chat", help="Inference mode")
    parser.add_argument("--prompt", type=str, help="Single prompt for inference")
    parser.add_argument("--prompts_file", type=str, help="File containing prompts for batch inference")
    
    args = parser.parse_args()
    
    # Initialize inference class
    llama = LlamaInference(args.model_path)
    
    if args.mode == "chat":
        # Interactive chat mode
        llama.chat_mode()
        
    elif args.mode == "single":
        # Single prompt inference
        if not args.prompt:
            print("Please provide a prompt using --prompt")
            return
        
        response = llama.generate_response(args.prompt)
        print(f"Prompt: {args.prompt}")
        print(f"Response: {response}")
        
    elif args.mode == "batch":
        # Batch inference
        if not args.prompts_file:
            print("Please provide a prompts file using --prompts_file")
            return
        
        try:
            with open(args.prompts_file, 'r') as f:
                prompts = [line.strip() for line in f if line.strip()]
            
            responses = llama.batch_inference(prompts)
            
            # Save results
            output_file = "batch_results.txt"
            with open(output_file, 'w') as f:
                for prompt, response in zip(prompts, responses):
                    f.write(f"Prompt: {prompt}\n")
                    f.write(f"Response: {response}\n")
                    f.write("-" * 50 + "\n")
            
            print(f"Batch inference completed. Results saved to {output_file}")
            
        except FileNotFoundError:
            print(f"File {args.prompts_file} not found")
        except Exception as e:
            print(f"Error during batch inference: {e}")


if __name__ == "__main__":
    main()


# Example usage:
"""
# Interactive chat
python inference_example.py --model_path ./llama3-finetuned --mode chat

# Single prompt
python inference_example.py --model_path ./llama3-finetuned --mode single --prompt "What is machine learning?"

# Batch inference
python inference_example.py --model_path ./llama3-finetuned --mode batch --prompts_file prompts.txt
"""
